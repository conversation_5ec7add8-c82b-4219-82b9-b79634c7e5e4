var j=Object.defineProperty;var b=t=>{throw TypeError(t)};var J=(t,s,e)=>s in t?j(t,s,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[s]=e;var i=(t,s,e)=>J(t,typeof s!="symbol"?s+"":s,e),w=(t,s,e)=>s.has(t)||b("Cannot "+e);var n=(t,s,e)=>(w(t,s,"read from private field"),e?e.call(t):s.get(t)),u=(t,s,e)=>s.has(t)?b("Cannot add the same private member more than once"):s instanceof WeakSet?s.add(t):s.set(t,e),x=(t,s,e,f)=>(w(t,s,"write to private field"),f?f.call(t,e):s.set(t,e),e);import{F as N,G as O,H as d,g as c,b as M,d as C,k}from"./xwHhHbJ3.js";import{A as y}from"./Deiq1e4r.js";const D=[];function E(t,s=!1){return p(t,new Map,"",D)}function p(t,s,e,f,m=null){if(typeof t=="object"&&t!==null){var S=s.get(t);if(S!==void 0)return S;if(t instanceof Map)return new Map(t);if(t instanceof Set)return new Set(t);if(N(t)){var r=Array(t.length);s.set(t,r),m!==null&&s.set(m,r);for(var o=0;o<t.length;o+=1){var A=t[o];o in t&&(r[o]=p(A,s,e,f))}return r}if(O(t)===d){r={},s.set(t,r),m!==null&&s.set(m,r);for(var _ in t)r[_]=p(t[_],s,e,f);return r}if(t instanceof Date)return structuredClone(t);if(typeof t.toJSON=="function")return p(t.toJSON(),s,e,f,t)}if(t instanceof EventTarget)return t;try{return structuredClone(t)}catch{return t}}class z extends y{constructor(s){super({...s,state:new F(s.messages)})}}var a,g,h;class F{constructor(s=[]){u(this,a);u(this,g,C("ready"));u(this,h,C(void 0));i(this,"setMessages",s=>{this.messages=s});i(this,"pushMessage",s=>{this.messages.push(s)});i(this,"popMessage",()=>{this.messages.pop()});i(this,"replaceMessage",(s,e)=>{this.messages[s]=e});i(this,"snapshot",s=>E(s));x(this,a,C(k(s)))}get messages(){return c(n(this,a))}set messages(s){M(n(this,a),s,!0)}get status(){return c(n(this,g))}set status(s){M(n(this,g),s,!0)}get error(){return c(n(this,h))}set error(s){M(n(this,h),s,!0)}}a=new WeakMap,g=new WeakMap,h=new WeakMap;export{z as C};
