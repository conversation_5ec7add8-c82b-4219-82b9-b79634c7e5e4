import{t as rs,a as ns}from"./Bc-255wv.js";import{y as as,B as ss,z as os,Y as is,C as Oe,aG as cs,aH as us,G as ls,aI as ds,U as Or,aJ as fs,aK as ps,j as hs,Z as ms,p as gs,t as vs,a as ys}from"./xwHhHbJ3.js";import{i as _s,c as bs,d as ws,a as xs,n as ks,b as Is,l as Ss,r as Ts}from"./CJ0gORsW.js";import{p as Nr,b as Es,r as zs}from"./C6NPqmlb.js";function As(e,t){var r=void 0,n;as(()=>{r!==(r=t())&&(n&&(ss(n),n=null),r&&(n=os(()=>{is(()=>r(e))})))})}function On(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(r=On(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function Nn(){for(var e,t,r=0,n="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=On(e))&&(n&&(n+=" "),n+=t);return n}function $s(e){return typeof e=="object"?Nn(e):e??""}const Cr=[...` 	
\r\f \v\uFEFF`];function Zs(e,t,r){var n=e==null?"":""+e;if(r){for(var a in r)if(r[a])n=n?n+" "+a:a;else if(n.length)for(var s=a.length,o=0;(o=n.indexOf(a,o))>=0;){var i=o+s;(o===0||Cr.includes(n[o-1]))&&(i===n.length||Cr.includes(n[i]))?n=(o===0?"":n.substring(0,o))+n.substring(i+1):o=i}}return n===""?null:n}function Pr(e,t=!1){var r=t?" !important;":";",n="";for(var a in e){var s=e[a];s!=null&&s!==""&&(n+=" "+a+": "+s+r)}return n}function Vt(e){return e[0]!=="-"||e[1]!=="-"?e.toLowerCase():e}function Os(e,t){if(t){var r="",n,a;if(Array.isArray(t)?(n=t[0],a=t[1]):n=t,e){e=String(e).replaceAll(/\s*\/\*.*?\*\/\s*/g,"").trim();var s=!1,o=0,i=!1,u=[];n&&u.push(...Object.keys(n).map(Vt)),a&&u.push(...Object.keys(a).map(Vt));var c=0,d=-1;const T=e.length;for(var g=0;g<T;g++){var x=e[g];if(i?x==="/"&&e[g-1]==="*"&&(i=!1):s?s===x&&(s=!1):x==="/"&&e[g+1]==="*"?i=!0:x==='"'||x==="'"?s=x:x==="("?o++:x===")"&&o--,!i&&s===!1&&o===0){if(x===":"&&d===-1)d=g;else if(x===";"||g===T-1){if(d!==-1){var A=Vt(e.substring(c,d).trim());if(!u.includes(A)){x!==";"&&g++;var b=e.substring(c,g).trim();r+=" "+b+";"}}c=g+1,d=-1}}}}return n&&(r+=Pr(n)),a&&(r+=Pr(a,!0)),r=r.trim(),r===""?null:r}return e==null?null:String(e)}function Ns(e,t,r,n,a,s){var o=e.__className;if(Oe||o!==r||o===void 0){var i=Zs(r,n,s);(!Oe||i!==e.getAttribute("class"))&&(i==null?e.removeAttribute("class"):t?e.className=i:e.setAttribute("class",i)),e.__className=r}else if(s&&a!==s)for(var u in s){var c=!!s[u];(a==null||c!==!!a[u])&&e.classList.toggle(u,c)}return s}function Bt(e,t={},r,n){for(var a in r){var s=r[a];t[a]!==s&&(r[a]==null?e.style.removeProperty(a):e.style.setProperty(a,s,n))}}function Cs(e,t,r,n){var a=e.__style;if(Oe||a!==t){var s=Os(t,n);(!Oe||s!==e.getAttribute("style"))&&(s==null?e.removeAttribute("style"):e.style.cssText=s),e.__style=t}else n&&(Array.isArray(n)?(Bt(e,r==null?void 0:r[0],n[0]),Bt(e,r==null?void 0:r[1],n[1],"important")):Bt(e,r,n));return n}const Ge=Symbol("class"),We=Symbol("style"),Cn=Symbol("is custom element"),Pn=Symbol("is html");function Ps(e,t){t?e.hasAttribute("selected")||e.setAttribute("selected",""):e.removeAttribute("selected")}function Rr(e,t,r,n){var a=Rn(e);Oe&&(a[t]=e.getAttribute(t),t==="src"||t==="srcset"||t==="href"&&e.nodeName==="LINK")||a[t]!==(a[t]=r)&&(t==="loading"&&(e[ds]=r),r==null?e.removeAttribute(t):typeof r!="string"&&Mn(e).includes(t)?e[t]=r:e.setAttribute(t,r))}function Rs(e,t,r,n,a=!1){var s=Rn(e),o=s[Cn],i=!s[Pn];let u=Oe&&o;u&&Or(!1);var c=t||{},d=e.tagName==="OPTION";for(var g in t)g in r||(r[g]=null);r.class?r.class=$s(r.class):r[Ge]&&(r.class=null),r[We]&&(r.style??(r.style=null));var x=Mn(e);for(const p in r){let f=r[p];if(d&&p==="value"&&f==null){e.value=e.__value="",c[p]=f;continue}if(p==="class"){var A=e.namespaceURI==="http://www.w3.org/1999/xhtml";Ns(e,A,f,n,t==null?void 0:t[Ge],r[Ge]),c[p]=f,c[Ge]=r[Ge];continue}if(p==="style"){Cs(e,f,t==null?void 0:t[We],r[We]),c[p]=f,c[We]=r[We];continue}var b=c[p];if(f!==b){c[p]=f;var T=p[0]+p[1];if(T!=="$$")if(T==="on"){const S={},_="$$"+p;let I=p.slice(2);var v=Is(I);if(_s(I)&&(I=I.slice(0,-7),S.capture=!0),!v&&b){if(f!=null)continue;e.removeEventListener(I,c[_],S),c[_]=null}if(f!=null)if(v)e[`__${I}`]=f,ws([I]);else{let J=function(de){c[p].call(this,de)};c[_]=bs(I,e,J,S)}else v&&(e[`__${I}`]=void 0)}else if(p==="style")Rr(e,p,f);else if(p==="autofocus")xs(e,!!f);else if(!o&&(p==="__value"||p==="value"&&f!=null))e.value=e.__value=f;else if(p==="selected"&&d)Ps(e,f);else{var l=p;i||(l=ks(l));var m=l==="defaultValue"||l==="defaultChecked";if(f==null&&!o&&!m)if(s[p]=null,l==="value"||l==="checked"){let S=e;const _=t===void 0;if(l==="value"){let I=S.defaultValue;S.removeAttribute(l),S.defaultValue=I,S.value=S.__value=_?I:null}else{let I=S.defaultChecked;S.removeAttribute(l),S.defaultChecked=I,S.checked=_?I:!1}}else e.removeAttribute(p);else m||x.includes(l)&&(o||typeof f!="string")?e[l]=f:typeof f!="function"&&Rr(e,l,f)}}}u&&Or(!0);for(let p of Object.getOwnPropertySymbols(r))p.description===cs&&As(e,()=>r[p]);return c}function Rn(e){return e.__attributes??(e.__attributes={[Cn]:e.nodeName.includes("-"),[Pn]:e.namespaceURI===us})}var Mr=new Map;function Mn(e){var t=Mr.get(e.nodeName);if(t)return t;Mr.set(e.nodeName,t=[]);for(var r,n=e,a=Element.prototype;a!==n;){r=fs(n);for(var s in r)r[s].set&&t.push(s);n=ls(n)}return t}function Ms(e,t,r=t){var n=ps();Ss(e,"input",a=>{var s=a?e.defaultValue:e.value;if(s=Jt(e)?Gt(s):s,r(s),n&&s!==(s=t())){var o=e.selectionStart,i=e.selectionEnd;e.value=s??"",i!==null&&(e.selectionStart=o,e.selectionEnd=Math.min(i,e.value.length))}}),(Oe&&e.defaultValue!==e.value||hs(t)==null&&e.value)&&r(Jt(e)?Gt(e.value):e.value),ms(()=>{var a=t();Jt(e)&&a===Gt(e.value)||e.type==="date"&&!a&&!e.value||a!==e.value&&(e.value=a??"")})}function Jt(e){var t=e.type;return t==="number"||t==="range"}function Gt(e){return e===""?null:+e}var jn="vercel.ai.error",js=Symbol.for(jn),Dn,Ds=class Ln extends Error{constructor({name:t,message:r,cause:n}){super(r),this[Dn]=!0,this.name=t,this.cause=n}static isInstance(t){return Ln.hasMarker(t,jn)}static hasMarker(t,r){const n=Symbol.for(r);return t!=null&&typeof t=="object"&&n in t&&typeof t[n]=="boolean"&&t[n]===!0}};Dn=js;var xe=Ds;function Un(e){return e==null?"unknown error":typeof e=="string"?e:e instanceof Error?e.message:JSON.stringify(e)}var Fn="AI_InvalidArgumentError",Vn=`vercel.ai.error.${Fn}`,Ls=Symbol.for(Vn),Bn,Us=class extends xe{constructor({message:e,cause:t,argument:r}){super({name:Fn,message:e,cause:t}),this[Bn]=!0,this.argument=r}static isInstance(e){return xe.hasMarker(e,Vn)}};Bn=Ls;var Jn="AI_JSONParseError",Gn=`vercel.ai.error.${Jn}`,Fs=Symbol.for(Gn),Wn,jr=class extends xe{constructor({text:e,cause:t}){super({name:Jn,message:`JSON parsing failed: Text: ${e}.
Error message: ${Un(t)}`,cause:t}),this[Wn]=!0,this.text=e}static isInstance(e){return xe.hasMarker(e,Gn)}};Wn=Fs;var qn="AI_TypeValidationError",Yn=`vercel.ai.error.${qn}`,Vs=Symbol.for(Yn),Kn,Bs=class er extends xe{constructor({value:t,cause:r}){super({name:qn,message:`Type validation failed: Value: ${JSON.stringify(t)}.
Error message: ${Un(r)}`,cause:r}),this[Kn]=!0,this.value=t}static isInstance(t){return xe.hasMarker(t,Yn)}static wrap({value:t,cause:r}){return er.isInstance(r)&&r.value===t?r:new er({value:t,cause:r})}};Kn=Vs;var kt=Bs;class Dr extends Error{constructor(t,r){super(t),this.name="ParseError",this.type=r.type,this.field=r.field,this.value=r.value,this.line=r.line}}function Wt(e){}function Js(e){if(typeof e=="function")throw new TypeError("`callbacks` must be an object, got a function instead. Did you mean `{onEvent: fn}`?");const{onEvent:t=Wt,onError:r=Wt,onRetry:n=Wt,onComment:a}=e;let s="",o=!0,i,u="",c="";function d(T){const v=o?T.replace(/^\xEF\xBB\xBF/,""):T,[l,m]=Gs(`${s}${v}`);for(const p of l)g(p);s=m,o=!1}function g(T){if(T===""){A();return}if(T.startsWith(":")){a&&a(T.slice(T.startsWith(": ")?2:1));return}const v=T.indexOf(":");if(v!==-1){const l=T.slice(0,v),m=T[v+1]===" "?2:1,p=T.slice(v+m);x(l,p,T);return}x(T,"",T)}function x(T,v,l){switch(T){case"event":c=v;break;case"data":u=`${u}${v}
`;break;case"id":i=v.includes("\0")?void 0:v;break;case"retry":/^\d+$/.test(v)?n(parseInt(v,10)):r(new Dr(`Invalid \`retry\` value: "${v}"`,{type:"invalid-retry",value:v,line:l}));break;default:r(new Dr(`Unknown field "${T.length>20?`${T.slice(0,20)}…`:T}"`,{type:"unknown-field",field:T,value:v,line:l}));break}}function A(){u.length>0&&t({id:i,event:c||void 0,data:u.endsWith(`
`)?u.slice(0,-1):u}),i=void 0,u="",c=""}function b(T={}){s&&T.consume&&g(s),o=!0,i=void 0,u="",c="",s=""}return{feed:d,reset:b}}function Gs(e){const t=[];let r="",n=0;for(;n<e.length;){const a=e.indexOf("\r",n),s=e.indexOf(`
`,n);let o=-1;if(a!==-1&&s!==-1?o=Math.min(a,s):a!==-1?o=a:s!==-1&&(o=s),o===-1){r=e.slice(n);break}else{const i=e.slice(n,o);t.push(i),n=o+1,e[n-1]==="\r"&&e[n]===`
`&&n++}}return[t,r]}class Ws extends TransformStream{constructor({onError:t,onRetry:r,onComment:n}={}){let a;super({start(s){a=Js({onEvent:o=>{s.enqueue(o)},onError(o){t==="terminate"?s.error(o):typeof t=="function"&&t(o)},onRetry:r,onComment:n})},transform(s){a.feed(s)}})}}function h(e,t,r){function n(i,u){var c;Object.defineProperty(i,"_zod",{value:i._zod??{},enumerable:!1}),(c=i._zod).traits??(c.traits=new Set),i._zod.traits.add(e),t(i,u);for(const d in o.prototype)d in i||Object.defineProperty(i,d,{value:o.prototype[d].bind(i)});i._zod.constr=o,i._zod.def=u}const a=(r==null?void 0:r.Parent)??Object;class s extends a{}Object.defineProperty(s,"name",{value:e});function o(i){var u;const c=r!=null&&r.Parent?new s:this;n(c,i),(u=c._zod).deferred??(u.deferred=[]);for(const d of c._zod.deferred)d();return c}return Object.defineProperty(o,"init",{value:n}),Object.defineProperty(o,Symbol.hasInstance,{value:i=>{var u,c;return r!=null&&r.Parent&&i instanceof r.Parent?!0:(c=(u=i==null?void 0:i._zod)==null?void 0:u.traits)==null?void 0:c.has(e)}}),Object.defineProperty(o,"name",{value:e}),o}class et extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}const Hn={};function ke(e){return Hn}function Xn(e){const t=Object.values(e).filter(n=>typeof n=="number");return Object.entries(e).filter(([n,a])=>t.indexOf(+n)===-1).map(([n,a])=>a)}function qs(e,t){return typeof t=="bigint"?t.toString():t}function mr(e){return{get value(){{const t=e();return Object.defineProperty(this,"value",{value:t}),t}}}}function gr(e){return e==null}function vr(e){const t=e.startsWith("^")?1:0,r=e.endsWith("$")?e.length-1:e.length;return e.slice(t,r)}function Ys(e,t){const r=(e.toString().split(".")[1]||"").length,n=(t.toString().split(".")[1]||"").length,a=r>n?r:n,s=Number.parseInt(e.toFixed(a).replace(".","")),o=Number.parseInt(t.toFixed(a).replace(".",""));return s%o/10**a}function V(e,t,r){Object.defineProperty(e,t,{get(){{const n=r();return e[t]=n,n}},set(n){Object.defineProperty(e,t,{value:n})},configurable:!0})}function ct(e,t,r){Object.defineProperty(e,t,{value:r,writable:!0,enumerable:!0,configurable:!0})}function qe(e){return JSON.stringify(e)}const Qn=Error.captureStackTrace?Error.captureStackTrace:(...e)=>{};function It(e){return typeof e=="object"&&e!==null&&!Array.isArray(e)}const Ks=mr(()=>{var e;if(typeof navigator<"u"&&((e=navigator==null?void 0:navigator.userAgent)!=null&&e.includes("Cloudflare")))return!1;try{const t=Function;return new t(""),!0}catch{return!1}});function St(e){if(It(e)===!1)return!1;const t=e.constructor;if(t===void 0)return!0;const r=t.prototype;return!(It(r)===!1||Object.prototype.hasOwnProperty.call(r,"isPrototypeOf")===!1)}const Hs=new Set(["string","number","symbol"]);function ut(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function Ce(e,t,r){const n=new e._zod.constr(t??e._zod.def);return(!t||r!=null&&r.parent)&&(n._zod.parent=e),n}function N(e){const t=e;if(!t)return{};if(typeof t=="string")return{error:()=>t};if((t==null?void 0:t.message)!==void 0){if((t==null?void 0:t.error)!==void 0)throw new Error("Cannot specify both `message` and `error` params");t.error=t.message}return delete t.message,typeof t.error=="string"?{...t,error:()=>t.error}:t}function Xs(e){return Object.keys(e).filter(t=>e[t]._zod.optin==="optional"&&e[t]._zod.optout==="optional")}const Qs={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-2147483648,2147483647],uint32:[0,4294967295],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]};function eo(e,t){const r={},n=e._zod.def;for(const a in t){if(!(a in n.shape))throw new Error(`Unrecognized key: "${a}"`);t[a]&&(r[a]=n.shape[a])}return Ce(e,{...e._zod.def,shape:r,checks:[]})}function to(e,t){const r={...e._zod.def.shape},n=e._zod.def;for(const a in t){if(!(a in n.shape))throw new Error(`Unrecognized key: "${a}"`);t[a]&&delete r[a]}return Ce(e,{...e._zod.def,shape:r,checks:[]})}function ro(e,t){if(!St(t))throw new Error("Invalid input to extend: expected a plain object");const r={...e._zod.def,get shape(){const n={...e._zod.def.shape,...t};return ct(this,"shape",n),n},checks:[]};return Ce(e,r)}function no(e,t){return Ce(e,{...e._zod.def,get shape(){const r={...e._zod.def.shape,...t._zod.def.shape};return ct(this,"shape",r),r},catchall:t._zod.def.catchall,checks:[]})}function ao(e,t,r){const n=t._zod.def.shape,a={...n};if(r)for(const s in r){if(!(s in n))throw new Error(`Unrecognized key: "${s}"`);r[s]&&(a[s]=e?new e({type:"optional",innerType:n[s]}):n[s])}else for(const s in n)a[s]=e?new e({type:"optional",innerType:n[s]}):n[s];return Ce(t,{...t._zod.def,shape:a,checks:[]})}function so(e,t,r){const n=t._zod.def.shape,a={...n};if(r)for(const s in r){if(!(s in a))throw new Error(`Unrecognized key: "${s}"`);r[s]&&(a[s]=new e({type:"nonoptional",innerType:n[s]}))}else for(const s in n)a[s]=new e({type:"nonoptional",innerType:n[s]});return Ce(t,{...t._zod.def,shape:a,checks:[]})}function Xe(e,t=0){var r;for(let n=t;n<e.issues.length;n++)if(((r=e.issues[n])==null?void 0:r.continue)!==!0)return!0;return!1}function $e(e,t){return t.map(r=>{var n;return(n=r).path??(n.path=[]),r.path.unshift(e),r})}function vt(e){return typeof e=="string"?e:e==null?void 0:e.message}function Ie(e,t,r){var a,s,o,i,u,c;const n={...e,path:e.path??[]};if(!e.message){const d=vt((o=(s=(a=e.inst)==null?void 0:a._zod.def)==null?void 0:s.error)==null?void 0:o.call(s,e))??vt((i=t==null?void 0:t.error)==null?void 0:i.call(t,e))??vt((u=r.customError)==null?void 0:u.call(r,e))??vt((c=r.localeError)==null?void 0:c.call(r,e))??"Invalid input";n.message=d}return delete n.inst,delete n.continue,t!=null&&t.reportInput||delete n.input,n}function yr(e){return Array.isArray(e)?"array":typeof e=="string"?"string":"unknown"}function tt(...e){const[t,r,n]=e;return typeof t=="string"?{message:t,code:"custom",input:r,inst:n}:{...t}}const ea=(e,t)=>{e.name="$ZodError",Object.defineProperty(e,"_zod",{value:e._zod,enumerable:!1}),Object.defineProperty(e,"issues",{value:t,enumerable:!1}),Object.defineProperty(e,"message",{get(){return JSON.stringify(t,qs,2)},enumerable:!0}),Object.defineProperty(e,"toString",{value:()=>e.message,enumerable:!1})},ta=h("$ZodError",ea),ra=h("$ZodError",ea,{Parent:Error});function oo(e,t=r=>r.message){const r={},n=[];for(const a of e.issues)a.path.length>0?(r[a.path[0]]=r[a.path[0]]||[],r[a.path[0]].push(t(a))):n.push(t(a));return{formErrors:n,fieldErrors:r}}function io(e,t){const r=t||function(s){return s.message},n={_errors:[]},a=s=>{for(const o of s.issues)if(o.code==="invalid_union"&&o.errors.length)o.errors.map(i=>a({issues:i}));else if(o.code==="invalid_key")a({issues:o.issues});else if(o.code==="invalid_element")a({issues:o.issues});else if(o.path.length===0)n._errors.push(r(o));else{let i=n,u=0;for(;u<o.path.length;){const c=o.path[u];u===o.path.length-1?(i[c]=i[c]||{_errors:[]},i[c]._errors.push(r(o))):i[c]=i[c]||{_errors:[]},i=i[c],u++}}};return a(e),n}const co=e=>(t,r,n,a)=>{const s=n?Object.assign(n,{async:!1}):{async:!1},o=t._zod.run({value:r,issues:[]},s);if(o instanceof Promise)throw new et;if(o.issues.length){const i=new((a==null?void 0:a.Err)??e)(o.issues.map(u=>Ie(u,s,ke())));throw Qn(i,a==null?void 0:a.callee),i}return o.value},uo=e=>async(t,r,n,a)=>{const s=n?Object.assign(n,{async:!0}):{async:!0};let o=t._zod.run({value:r,issues:[]},s);if(o instanceof Promise&&(o=await o),o.issues.length){const i=new((a==null?void 0:a.Err)??e)(o.issues.map(u=>Ie(u,s,ke())));throw Qn(i,a==null?void 0:a.callee),i}return o.value},na=e=>(t,r,n)=>{const a=n?{...n,async:!1}:{async:!1},s=t._zod.run({value:r,issues:[]},a);if(s instanceof Promise)throw new et;return s.issues.length?{success:!1,error:new(e??ta)(s.issues.map(o=>Ie(o,a,ke())))}:{success:!0,data:s.value}},lo=na(ra),aa=e=>async(t,r,n)=>{const a=n?Object.assign(n,{async:!0}):{async:!0};let s=t._zod.run({value:r,issues:[]},a);return s instanceof Promise&&(s=await s),s.issues.length?{success:!1,error:new e(s.issues.map(o=>Ie(o,a,ke())))}:{success:!0,data:s.value}},fo=aa(ra),po=/^[cC][^\s-]{8,}$/,ho=/^[0-9a-z]+$/,mo=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,go=/^[0-9a-vA-V]{20}$/,vo=/^[A-Za-z0-9]{27}$/,yo=/^[a-zA-Z0-9_-]{21}$/,_o=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,bo=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,Lr=e=>e?new RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${e}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$/,wo=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,xo="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";function ko(){return new RegExp(xo,"u")}const Io=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,So=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,To=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,Eo=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,zo=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,sa=/^[A-Za-z0-9_-]*$/,Ao=/^([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+$/,$o=/^\+(?:[0-9]){6,14}[0-9]$/,oa="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",Zo=new RegExp(`^${oa}$`);function ia(e){const t="(?:[01]\\d|2[0-3]):[0-5]\\d";return typeof e.precision=="number"?e.precision===-1?`${t}`:e.precision===0?`${t}:[0-5]\\d`:`${t}:[0-5]\\d\\.\\d{${e.precision}}`:`${t}(?::[0-5]\\d(?:\\.\\d+)?)?`}function Oo(e){return new RegExp(`^${ia(e)}$`)}function No(e){const t=ia({precision:e.precision}),r=["Z"];e.local&&r.push(""),e.offset&&r.push("([+-]\\d{2}:\\d{2})");const n=`${t}(?:${r.join("|")})`;return new RegExp(`^${oa}T(?:${n})$`)}const Co=e=>{const t=e?`[\\s\\S]{${(e==null?void 0:e.minimum)??0},${(e==null?void 0:e.maximum)??""}}`:"[\\s\\S]*";return new RegExp(`^${t}$`)},Po=/^\d+$/,Ro=/^-?\d+(?:\.\d+)?/i,Mo=/true|false/i,jo=/null/i,Do=/^[^A-Z]*$/,Lo=/^[^a-z]*$/,ce=h("$ZodCheck",(e,t)=>{var r;e._zod??(e._zod={}),e._zod.def=t,(r=e._zod).onattach??(r.onattach=[])}),ca={number:"number",bigint:"bigint",object:"date"},ua=h("$ZodCheckLessThan",(e,t)=>{ce.init(e,t);const r=ca[typeof t.value];e._zod.onattach.push(n=>{const a=n._zod.bag,s=(t.inclusive?a.maximum:a.exclusiveMaximum)??Number.POSITIVE_INFINITY;t.value<s&&(t.inclusive?a.maximum=t.value:a.exclusiveMaximum=t.value)}),e._zod.check=n=>{(t.inclusive?n.value<=t.value:n.value<t.value)||n.issues.push({origin:r,code:"too_big",maximum:t.value,input:n.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),la=h("$ZodCheckGreaterThan",(e,t)=>{ce.init(e,t);const r=ca[typeof t.value];e._zod.onattach.push(n=>{const a=n._zod.bag,s=(t.inclusive?a.minimum:a.exclusiveMinimum)??Number.NEGATIVE_INFINITY;t.value>s&&(t.inclusive?a.minimum=t.value:a.exclusiveMinimum=t.value)}),e._zod.check=n=>{(t.inclusive?n.value>=t.value:n.value>t.value)||n.issues.push({origin:r,code:"too_small",minimum:t.value,input:n.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),Uo=h("$ZodCheckMultipleOf",(e,t)=>{ce.init(e,t),e._zod.onattach.push(r=>{var n;(n=r._zod.bag).multipleOf??(n.multipleOf=t.value)}),e._zod.check=r=>{if(typeof r.value!=typeof t.value)throw new Error("Cannot mix number and bigint in multiple_of check.");(typeof r.value=="bigint"?r.value%t.value===BigInt(0):Ys(r.value,t.value)===0)||r.issues.push({origin:typeof r.value,code:"not_multiple_of",divisor:t.value,input:r.value,inst:e,continue:!t.abort})}}),Fo=h("$ZodCheckNumberFormat",(e,t)=>{var o;ce.init(e,t),t.format=t.format||"float64";const r=(o=t.format)==null?void 0:o.includes("int"),n=r?"int":"number",[a,s]=Qs[t.format];e._zod.onattach.push(i=>{const u=i._zod.bag;u.format=t.format,u.minimum=a,u.maximum=s,r&&(u.pattern=Po)}),e._zod.check=i=>{const u=i.value;if(r){if(!Number.isInteger(u)){i.issues.push({expected:n,format:t.format,code:"invalid_type",input:u,inst:e});return}if(!Number.isSafeInteger(u)){u>0?i.issues.push({input:u,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:n,continue:!t.abort}):i.issues.push({input:u,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:n,continue:!t.abort});return}}u<a&&i.issues.push({origin:"number",input:u,code:"too_small",minimum:a,inclusive:!0,inst:e,continue:!t.abort}),u>s&&i.issues.push({origin:"number",input:u,code:"too_big",maximum:s,inst:e})}}),Vo=h("$ZodCheckMaxLength",(e,t)=>{var r;ce.init(e,t),(r=e._zod.def).when??(r.when=n=>{const a=n.value;return!gr(a)&&a.length!==void 0}),e._zod.onattach.push(n=>{const a=n._zod.bag.maximum??Number.POSITIVE_INFINITY;t.maximum<a&&(n._zod.bag.maximum=t.maximum)}),e._zod.check=n=>{const a=n.value;if(a.length<=t.maximum)return;const o=yr(a);n.issues.push({origin:o,code:"too_big",maximum:t.maximum,inclusive:!0,input:a,inst:e,continue:!t.abort})}}),Bo=h("$ZodCheckMinLength",(e,t)=>{var r;ce.init(e,t),(r=e._zod.def).when??(r.when=n=>{const a=n.value;return!gr(a)&&a.length!==void 0}),e._zod.onattach.push(n=>{const a=n._zod.bag.minimum??Number.NEGATIVE_INFINITY;t.minimum>a&&(n._zod.bag.minimum=t.minimum)}),e._zod.check=n=>{const a=n.value;if(a.length>=t.minimum)return;const o=yr(a);n.issues.push({origin:o,code:"too_small",minimum:t.minimum,inclusive:!0,input:a,inst:e,continue:!t.abort})}}),Jo=h("$ZodCheckLengthEquals",(e,t)=>{var r;ce.init(e,t),(r=e._zod.def).when??(r.when=n=>{const a=n.value;return!gr(a)&&a.length!==void 0}),e._zod.onattach.push(n=>{const a=n._zod.bag;a.minimum=t.length,a.maximum=t.length,a.length=t.length}),e._zod.check=n=>{const a=n.value,s=a.length;if(s===t.length)return;const o=yr(a),i=s>t.length;n.issues.push({origin:o,...i?{code:"too_big",maximum:t.length}:{code:"too_small",minimum:t.length},inclusive:!0,exact:!0,input:n.value,inst:e,continue:!t.abort})}}),Rt=h("$ZodCheckStringFormat",(e,t)=>{var r,n;ce.init(e,t),e._zod.onattach.push(a=>{const s=a._zod.bag;s.format=t.format,t.pattern&&(s.patterns??(s.patterns=new Set),s.patterns.add(t.pattern))}),t.pattern?(r=e._zod).check??(r.check=a=>{t.pattern.lastIndex=0,!t.pattern.test(a.value)&&a.issues.push({origin:"string",code:"invalid_format",format:t.format,input:a.value,...t.pattern?{pattern:t.pattern.toString()}:{},inst:e,continue:!t.abort})}):(n=e._zod).check??(n.check=()=>{})}),Go=h("$ZodCheckRegex",(e,t)=>{Rt.init(e,t),e._zod.check=r=>{t.pattern.lastIndex=0,!t.pattern.test(r.value)&&r.issues.push({origin:"string",code:"invalid_format",format:"regex",input:r.value,pattern:t.pattern.toString(),inst:e,continue:!t.abort})}}),Wo=h("$ZodCheckLowerCase",(e,t)=>{t.pattern??(t.pattern=Do),Rt.init(e,t)}),qo=h("$ZodCheckUpperCase",(e,t)=>{t.pattern??(t.pattern=Lo),Rt.init(e,t)}),Yo=h("$ZodCheckIncludes",(e,t)=>{ce.init(e,t);const r=ut(t.includes),n=new RegExp(typeof t.position=="number"?`^.{${t.position}}${r}`:r);t.pattern=n,e._zod.onattach.push(a=>{const s=a._zod.bag;s.patterns??(s.patterns=new Set),s.patterns.add(n)}),e._zod.check=a=>{a.value.includes(t.includes,t.position)||a.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:t.includes,input:a.value,inst:e,continue:!t.abort})}}),Ko=h("$ZodCheckStartsWith",(e,t)=>{ce.init(e,t);const r=new RegExp(`^${ut(t.prefix)}.*`);t.pattern??(t.pattern=r),e._zod.onattach.push(n=>{const a=n._zod.bag;a.patterns??(a.patterns=new Set),a.patterns.add(r)}),e._zod.check=n=>{n.value.startsWith(t.prefix)||n.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:t.prefix,input:n.value,inst:e,continue:!t.abort})}}),Ho=h("$ZodCheckEndsWith",(e,t)=>{ce.init(e,t);const r=new RegExp(`.*${ut(t.suffix)}$`);t.pattern??(t.pattern=r),e._zod.onattach.push(n=>{const a=n._zod.bag;a.patterns??(a.patterns=new Set),a.patterns.add(r)}),e._zod.check=n=>{n.value.endsWith(t.suffix)||n.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:t.suffix,input:n.value,inst:e,continue:!t.abort})}}),Xo=h("$ZodCheckOverwrite",(e,t)=>{ce.init(e,t),e._zod.check=r=>{r.value=t.tx(r.value)}});class Qo{constructor(t=[]){this.content=[],this.indent=0,this&&(this.args=t)}indented(t){this.indent+=1,t(this),this.indent-=1}write(t){if(typeof t=="function"){t(this,{execution:"sync"}),t(this,{execution:"async"});return}const n=t.split(`
`).filter(o=>o),a=Math.min(...n.map(o=>o.length-o.trimStart().length)),s=n.map(o=>o.slice(a)).map(o=>" ".repeat(this.indent*2)+o);for(const o of s)this.content.push(o)}compile(){const t=Function,r=this==null?void 0:this.args,a=[...((this==null?void 0:this.content)??[""]).map(s=>`  ${s}`)];return new t(...r,a.join(`
`))}}const ei={major:4,minor:0,patch:0},W=h("$ZodType",(e,t)=>{var a;var r;e??(e={}),e._zod.def=t,e._zod.bag=e._zod.bag||{},e._zod.version=ei;const n=[...e._zod.def.checks??[]];e._zod.traits.has("$ZodCheck")&&n.unshift(e);for(const s of n)for(const o of s._zod.onattach)o(e);if(n.length===0)(r=e._zod).deferred??(r.deferred=[]),(a=e._zod.deferred)==null||a.push(()=>{e._zod.run=e._zod.parse});else{const s=(o,i,u)=>{let c=Xe(o),d;for(const g of i){if(g._zod.def.when){if(!g._zod.def.when(o))continue}else if(c)continue;const x=o.issues.length,A=g._zod.check(o);if(A instanceof Promise&&(u==null?void 0:u.async)===!1)throw new et;if(d||A instanceof Promise)d=(d??Promise.resolve()).then(async()=>{await A,o.issues.length!==x&&(c||(c=Xe(o,x)))});else{if(o.issues.length===x)continue;c||(c=Xe(o,x))}}return d?d.then(()=>o):o};e._zod.run=(o,i)=>{const u=e._zod.parse(o,i);if(u instanceof Promise){if(i.async===!1)throw new et;return u.then(c=>s(c,n,i))}return s(u,n,i)}}e["~standard"]={validate:s=>{var o;try{const i=lo(e,s);return i.success?{value:i.data}:{issues:(o=i.error)==null?void 0:o.issues}}catch{return fo(e,s).then(u=>{var c;return u.success?{value:u.data}:{issues:(c=u.error)==null?void 0:c.issues}})}},vendor:"zod",version:1}}),_r=h("$ZodString",(e,t)=>{var r;W.init(e,t),e._zod.pattern=[...((r=e==null?void 0:e._zod.bag)==null?void 0:r.patterns)??[]].pop()??Co(e._zod.bag),e._zod.parse=(n,a)=>{if(t.coerce)try{n.value=String(n.value)}catch{}return typeof n.value=="string"||n.issues.push({expected:"string",code:"invalid_type",input:n.value,inst:e}),n}}),K=h("$ZodStringFormat",(e,t)=>{Rt.init(e,t),_r.init(e,t)}),ti=h("$ZodGUID",(e,t)=>{t.pattern??(t.pattern=bo),K.init(e,t)}),ri=h("$ZodUUID",(e,t)=>{if(t.version){const n={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[t.version];if(n===void 0)throw new Error(`Invalid UUID version: "${t.version}"`);t.pattern??(t.pattern=Lr(n))}else t.pattern??(t.pattern=Lr());K.init(e,t)}),ni=h("$ZodEmail",(e,t)=>{t.pattern??(t.pattern=wo),K.init(e,t)}),ai=h("$ZodURL",(e,t)=>{K.init(e,t),e._zod.check=r=>{try{const n=r.value,a=new URL(n),s=a.href;t.hostname&&(t.hostname.lastIndex=0,t.hostname.test(a.hostname)||r.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:Ao.source,input:r.value,inst:e,continue:!t.abort})),t.protocol&&(t.protocol.lastIndex=0,t.protocol.test(a.protocol.endsWith(":")?a.protocol.slice(0,-1):a.protocol)||r.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:t.protocol.source,input:r.value,inst:e,continue:!t.abort})),!n.endsWith("/")&&s.endsWith("/")?r.value=s.slice(0,-1):r.value=s;return}catch{r.issues.push({code:"invalid_format",format:"url",input:r.value,inst:e,continue:!t.abort})}}}),si=h("$ZodEmoji",(e,t)=>{t.pattern??(t.pattern=ko()),K.init(e,t)}),oi=h("$ZodNanoID",(e,t)=>{t.pattern??(t.pattern=yo),K.init(e,t)}),ii=h("$ZodCUID",(e,t)=>{t.pattern??(t.pattern=po),K.init(e,t)}),ci=h("$ZodCUID2",(e,t)=>{t.pattern??(t.pattern=ho),K.init(e,t)}),ui=h("$ZodULID",(e,t)=>{t.pattern??(t.pattern=mo),K.init(e,t)}),li=h("$ZodXID",(e,t)=>{t.pattern??(t.pattern=go),K.init(e,t)}),di=h("$ZodKSUID",(e,t)=>{t.pattern??(t.pattern=vo),K.init(e,t)}),fi=h("$ZodISODateTime",(e,t)=>{t.pattern??(t.pattern=No(t)),K.init(e,t)}),pi=h("$ZodISODate",(e,t)=>{t.pattern??(t.pattern=Zo),K.init(e,t)}),hi=h("$ZodISOTime",(e,t)=>{t.pattern??(t.pattern=Oo(t)),K.init(e,t)}),mi=h("$ZodISODuration",(e,t)=>{t.pattern??(t.pattern=_o),K.init(e,t)}),gi=h("$ZodIPv4",(e,t)=>{t.pattern??(t.pattern=Io),K.init(e,t),e._zod.onattach.push(r=>{const n=r._zod.bag;n.format="ipv4"})}),vi=h("$ZodIPv6",(e,t)=>{t.pattern??(t.pattern=So),K.init(e,t),e._zod.onattach.push(r=>{const n=r._zod.bag;n.format="ipv6"}),e._zod.check=r=>{try{new URL(`http://[${r.value}]`)}catch{r.issues.push({code:"invalid_format",format:"ipv6",input:r.value,inst:e,continue:!t.abort})}}}),yi=h("$ZodCIDRv4",(e,t)=>{t.pattern??(t.pattern=To),K.init(e,t)}),_i=h("$ZodCIDRv6",(e,t)=>{t.pattern??(t.pattern=Eo),K.init(e,t),e._zod.check=r=>{const[n,a]=r.value.split("/");try{if(!a)throw new Error;const s=Number(a);if(`${s}`!==a)throw new Error;if(s<0||s>128)throw new Error;new URL(`http://[${n}]`)}catch{r.issues.push({code:"invalid_format",format:"cidrv6",input:r.value,inst:e,continue:!t.abort})}}});function da(e){if(e==="")return!0;if(e.length%4!==0)return!1;try{return atob(e),!0}catch{return!1}}const bi=h("$ZodBase64",(e,t)=>{t.pattern??(t.pattern=zo),K.init(e,t),e._zod.onattach.push(r=>{r._zod.bag.contentEncoding="base64"}),e._zod.check=r=>{da(r.value)||r.issues.push({code:"invalid_format",format:"base64",input:r.value,inst:e,continue:!t.abort})}});function wi(e){if(!sa.test(e))return!1;const t=e.replace(/[-_]/g,n=>n==="-"?"+":"/"),r=t.padEnd(Math.ceil(t.length/4)*4,"=");return da(r)}const xi=h("$ZodBase64URL",(e,t)=>{t.pattern??(t.pattern=sa),K.init(e,t),e._zod.onattach.push(r=>{r._zod.bag.contentEncoding="base64url"}),e._zod.check=r=>{wi(r.value)||r.issues.push({code:"invalid_format",format:"base64url",input:r.value,inst:e,continue:!t.abort})}}),ki=h("$ZodE164",(e,t)=>{t.pattern??(t.pattern=$o),K.init(e,t)});function Ii(e,t=null){try{const r=e.split(".");if(r.length!==3)return!1;const[n]=r;if(!n)return!1;const a=JSON.parse(atob(n));return!("typ"in a&&(a==null?void 0:a.typ)!=="JWT"||!a.alg||t&&(!("alg"in a)||a.alg!==t))}catch{return!1}}const Si=h("$ZodJWT",(e,t)=>{K.init(e,t),e._zod.check=r=>{Ii(r.value,t.alg)||r.issues.push({code:"invalid_format",format:"jwt",input:r.value,inst:e,continue:!t.abort})}}),fa=h("$ZodNumber",(e,t)=>{W.init(e,t),e._zod.pattern=e._zod.bag.pattern??Ro,e._zod.parse=(r,n)=>{if(t.coerce)try{r.value=Number(r.value)}catch{}const a=r.value;if(typeof a=="number"&&!Number.isNaN(a)&&Number.isFinite(a))return r;const s=typeof a=="number"?Number.isNaN(a)?"NaN":Number.isFinite(a)?void 0:"Infinity":void 0;return r.issues.push({expected:"number",code:"invalid_type",input:a,inst:e,...s?{received:s}:{}}),r}}),Ti=h("$ZodNumber",(e,t)=>{Fo.init(e,t),fa.init(e,t)}),Ei=h("$ZodBoolean",(e,t)=>{W.init(e,t),e._zod.pattern=Mo,e._zod.parse=(r,n)=>{if(t.coerce)try{r.value=!!r.value}catch{}const a=r.value;return typeof a=="boolean"||r.issues.push({expected:"boolean",code:"invalid_type",input:a,inst:e}),r}}),zi=h("$ZodNull",(e,t)=>{W.init(e,t),e._zod.pattern=jo,e._zod.values=new Set([null]),e._zod.parse=(r,n)=>{const a=r.value;return a===null||r.issues.push({expected:"null",code:"invalid_type",input:a,inst:e}),r}}),Ai=h("$ZodUnknown",(e,t)=>{W.init(e,t),e._zod.parse=r=>r}),$i=h("$ZodNever",(e,t)=>{W.init(e,t),e._zod.parse=(r,n)=>(r.issues.push({expected:"never",code:"invalid_type",input:r.value,inst:e}),r)});function Ur(e,t,r){e.issues.length&&t.issues.push(...$e(r,e.issues)),t.value[r]=e.value}const Zi=h("$ZodArray",(e,t)=>{W.init(e,t),e._zod.parse=(r,n)=>{const a=r.value;if(!Array.isArray(a))return r.issues.push({expected:"array",code:"invalid_type",input:a,inst:e}),r;r.value=Array(a.length);const s=[];for(let o=0;o<a.length;o++){const i=a[o],u=t.element._zod.run({value:i,issues:[]},n);u instanceof Promise?s.push(u.then(c=>Ur(c,r,o))):Ur(u,r,o)}return s.length?Promise.all(s).then(()=>r):r}});function yt(e,t,r){e.issues.length&&t.issues.push(...$e(r,e.issues)),t.value[r]=e.value}function Fr(e,t,r,n){e.issues.length?n[r]===void 0?r in n?t.value[r]=void 0:t.value[r]=e.value:t.issues.push(...$e(r,e.issues)):e.value===void 0?r in n&&(t.value[r]=void 0):t.value[r]=e.value}const Oi=h("$ZodObject",(e,t)=>{W.init(e,t);const r=mr(()=>{const g=Object.keys(t.shape);for(const A of g)if(!(t.shape[A]instanceof W))throw new Error(`Invalid element at key "${A}": expected a Zod schema`);const x=Xs(t.shape);return{shape:t.shape,keys:g,keySet:new Set(g),numKeys:g.length,optionalKeys:new Set(x)}});V(e._zod,"propValues",()=>{const g=t.shape,x={};for(const A in g){const b=g[A]._zod;if(b.values){x[A]??(x[A]=new Set);for(const T of b.values)x[A].add(T)}}return x});const n=g=>{const x=new Qo(["shape","payload","ctx"]),A=r.value,b=m=>{const p=qe(m);return`shape[${p}]._zod.run({ value: input[${p}], issues: [] }, ctx)`};x.write("const input = payload.value;");const T=Object.create(null);let v=0;for(const m of A.keys)T[m]=`key_${v++}`;x.write("const newResult = {}");for(const m of A.keys)if(A.optionalKeys.has(m)){const p=T[m];x.write(`const ${p} = ${b(m)};`);const f=qe(m);x.write(`
        if (${p}.issues.length) {
          if (input[${f}] === undefined) {
            if (${f} in input) {
              newResult[${f}] = undefined;
            }
          } else {
            payload.issues = payload.issues.concat(
              ${p}.issues.map((iss) => ({
                ...iss,
                path: iss.path ? [${f}, ...iss.path] : [${f}],
              }))
            );
          }
        } else if (${p}.value === undefined) {
          if (${f} in input) newResult[${f}] = undefined;
        } else {
          newResult[${f}] = ${p}.value;
        }
        `)}else{const p=T[m];x.write(`const ${p} = ${b(m)};`),x.write(`
          if (${p}.issues.length) payload.issues = payload.issues.concat(${p}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${qe(m)}, ...iss.path] : [${qe(m)}]
          })));`),x.write(`newResult[${qe(m)}] = ${p}.value`)}x.write("payload.value = newResult;"),x.write("return payload;");const l=x.compile();return(m,p)=>l(g,m,p)};let a;const s=It,o=!Hn.jitless,u=o&&Ks.value,c=t.catchall;let d;e._zod.parse=(g,x)=>{d??(d=r.value);const A=g.value;if(!s(A))return g.issues.push({expected:"object",code:"invalid_type",input:A,inst:e}),g;const b=[];if(o&&u&&(x==null?void 0:x.async)===!1&&x.jitless!==!0)a||(a=n(t.shape)),g=a(g,x);else{g.value={};const p=d.shape;for(const f of d.keys){const S=p[f],_=S._zod.run({value:A[f],issues:[]},x),I=S._zod.optin==="optional"&&S._zod.optout==="optional";_ instanceof Promise?b.push(_.then(J=>I?Fr(J,g,f,A):yt(J,g,f))):I?Fr(_,g,f,A):yt(_,g,f)}}if(!c)return b.length?Promise.all(b).then(()=>g):g;const T=[],v=d.keySet,l=c._zod,m=l.def.type;for(const p of Object.keys(A)){if(v.has(p))continue;if(m==="never"){T.push(p);continue}const f=l.run({value:A[p],issues:[]},x);f instanceof Promise?b.push(f.then(S=>yt(S,g,p))):yt(f,g,p)}return T.length&&g.issues.push({code:"unrecognized_keys",keys:T,input:A,inst:e}),b.length?Promise.all(b).then(()=>g):g}});function Vr(e,t,r,n){for(const a of e)if(a.issues.length===0)return t.value=a.value,t;return t.issues.push({code:"invalid_union",input:t.value,inst:r,errors:e.map(a=>a.issues.map(s=>Ie(s,n,ke())))}),t}const pa=h("$ZodUnion",(e,t)=>{W.init(e,t),V(e._zod,"optin",()=>t.options.some(r=>r._zod.optin==="optional")?"optional":void 0),V(e._zod,"optout",()=>t.options.some(r=>r._zod.optout==="optional")?"optional":void 0),V(e._zod,"values",()=>{if(t.options.every(r=>r._zod.values))return new Set(t.options.flatMap(r=>Array.from(r._zod.values)))}),V(e._zod,"pattern",()=>{if(t.options.every(r=>r._zod.pattern)){const r=t.options.map(n=>n._zod.pattern);return new RegExp(`^(${r.map(n=>vr(n.source)).join("|")})$`)}}),e._zod.parse=(r,n)=>{let a=!1;const s=[];for(const o of t.options){const i=o._zod.run({value:r.value,issues:[]},n);if(i instanceof Promise)s.push(i),a=!0;else{if(i.issues.length===0)return i;s.push(i)}}return a?Promise.all(s).then(o=>Vr(o,r,e,n)):Vr(s,r,e,n)}}),Ni=h("$ZodDiscriminatedUnion",(e,t)=>{pa.init(e,t);const r=e._zod.parse;V(e._zod,"propValues",()=>{const a={};for(const s of t.options){const o=s._zod.propValues;if(!o||Object.keys(o).length===0)throw new Error(`Invalid discriminated union option at index "${t.options.indexOf(s)}"`);for(const[i,u]of Object.entries(o)){a[i]||(a[i]=new Set);for(const c of u)a[i].add(c)}}return a});const n=mr(()=>{const a=t.options,s=new Map;for(const o of a){const i=o._zod.propValues[t.discriminator];if(!i||i.size===0)throw new Error(`Invalid discriminated union option at index "${t.options.indexOf(o)}"`);for(const u of i){if(s.has(u))throw new Error(`Duplicate discriminator value "${String(u)}"`);s.set(u,o)}}return s});e._zod.parse=(a,s)=>{const o=a.value;if(!It(o))return a.issues.push({code:"invalid_type",expected:"object",input:o,inst:e}),a;const i=n.value.get(o==null?void 0:o[t.discriminator]);return i?i._zod.run(a,s):t.unionFallback?r(a,s):(a.issues.push({code:"invalid_union",errors:[],note:"No matching discriminator",input:o,path:[t.discriminator],inst:e}),a)}}),Ci=h("$ZodIntersection",(e,t)=>{W.init(e,t),e._zod.parse=(r,n)=>{const a=r.value,s=t.left._zod.run({value:a,issues:[]},n),o=t.right._zod.run({value:a,issues:[]},n);return s instanceof Promise||o instanceof Promise?Promise.all([s,o]).then(([u,c])=>Br(r,u,c)):Br(r,s,o)}});function tr(e,t){if(e===t)return{valid:!0,data:e};if(e instanceof Date&&t instanceof Date&&+e==+t)return{valid:!0,data:e};if(St(e)&&St(t)){const r=Object.keys(t),n=Object.keys(e).filter(s=>r.indexOf(s)!==-1),a={...e,...t};for(const s of n){const o=tr(e[s],t[s]);if(!o.valid)return{valid:!1,mergeErrorPath:[s,...o.mergeErrorPath]};a[s]=o.data}return{valid:!0,data:a}}if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return{valid:!1,mergeErrorPath:[]};const r=[];for(let n=0;n<e.length;n++){const a=e[n],s=t[n],o=tr(a,s);if(!o.valid)return{valid:!1,mergeErrorPath:[n,...o.mergeErrorPath]};r.push(o.data)}return{valid:!0,data:r}}return{valid:!1,mergeErrorPath:[]}}function Br(e,t,r){if(t.issues.length&&e.issues.push(...t.issues),r.issues.length&&e.issues.push(...r.issues),Xe(e))return e;const n=tr(t.value,r.value);if(!n.valid)throw new Error(`Unmergable intersection. Error path: ${JSON.stringify(n.mergeErrorPath)}`);return e.value=n.data,e}const Pi=h("$ZodRecord",(e,t)=>{W.init(e,t),e._zod.parse=(r,n)=>{const a=r.value;if(!St(a))return r.issues.push({expected:"record",code:"invalid_type",input:a,inst:e}),r;const s=[];if(t.keyType._zod.values){const o=t.keyType._zod.values;r.value={};for(const u of o)if(typeof u=="string"||typeof u=="number"||typeof u=="symbol"){const c=t.valueType._zod.run({value:a[u],issues:[]},n);c instanceof Promise?s.push(c.then(d=>{d.issues.length&&r.issues.push(...$e(u,d.issues)),r.value[u]=d.value})):(c.issues.length&&r.issues.push(...$e(u,c.issues)),r.value[u]=c.value)}let i;for(const u in a)o.has(u)||(i=i??[],i.push(u));i&&i.length>0&&r.issues.push({code:"unrecognized_keys",input:a,inst:e,keys:i})}else{r.value={};for(const o of Reflect.ownKeys(a)){if(o==="__proto__")continue;const i=t.keyType._zod.run({value:o,issues:[]},n);if(i instanceof Promise)throw new Error("Async schemas not supported in object keys currently");if(i.issues.length){r.issues.push({origin:"record",code:"invalid_key",issues:i.issues.map(c=>Ie(c,n,ke())),input:o,path:[o],inst:e}),r.value[i.value]=i.value;continue}const u=t.valueType._zod.run({value:a[o],issues:[]},n);u instanceof Promise?s.push(u.then(c=>{c.issues.length&&r.issues.push(...$e(o,c.issues)),r.value[i.value]=c.value})):(u.issues.length&&r.issues.push(...$e(o,u.issues)),r.value[i.value]=u.value)}}return s.length?Promise.all(s).then(()=>r):r}}),Ri=h("$ZodEnum",(e,t)=>{W.init(e,t);const r=Xn(t.entries);e._zod.values=new Set(r),e._zod.pattern=new RegExp(`^(${r.filter(n=>Hs.has(typeof n)).map(n=>typeof n=="string"?ut(n):n.toString()).join("|")})$`),e._zod.parse=(n,a)=>{const s=n.value;return e._zod.values.has(s)||n.issues.push({code:"invalid_value",values:r,input:s,inst:e}),n}}),Mi=h("$ZodLiteral",(e,t)=>{W.init(e,t),e._zod.values=new Set(t.values),e._zod.pattern=new RegExp(`^(${t.values.map(r=>typeof r=="string"?ut(r):r?r.toString():String(r)).join("|")})$`),e._zod.parse=(r,n)=>{const a=r.value;return e._zod.values.has(a)||r.issues.push({code:"invalid_value",values:t.values,input:a,inst:e}),r}}),ji=h("$ZodTransform",(e,t)=>{W.init(e,t),e._zod.parse=(r,n)=>{const a=t.transform(r.value,r);if(n.async)return(a instanceof Promise?a:Promise.resolve(a)).then(o=>(r.value=o,r));if(a instanceof Promise)throw new et;return r.value=a,r}}),Di=h("$ZodOptional",(e,t)=>{W.init(e,t),e._zod.optin="optional",e._zod.optout="optional",V(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,void 0]):void 0),V(e._zod,"pattern",()=>{const r=t.innerType._zod.pattern;return r?new RegExp(`^(${vr(r.source)})?$`):void 0}),e._zod.parse=(r,n)=>t.innerType._zod.optin==="optional"?t.innerType._zod.run(r,n):r.value===void 0?r:t.innerType._zod.run(r,n)}),Li=h("$ZodNullable",(e,t)=>{W.init(e,t),V(e._zod,"optin",()=>t.innerType._zod.optin),V(e._zod,"optout",()=>t.innerType._zod.optout),V(e._zod,"pattern",()=>{const r=t.innerType._zod.pattern;return r?new RegExp(`^(${vr(r.source)}|null)$`):void 0}),V(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,null]):void 0),e._zod.parse=(r,n)=>r.value===null?r:t.innerType._zod.run(r,n)}),Ui=h("$ZodDefault",(e,t)=>{W.init(e,t),e._zod.optin="optional",V(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(r,n)=>{if(r.value===void 0)return r.value=t.defaultValue,r;const a=t.innerType._zod.run(r,n);return a instanceof Promise?a.then(s=>Jr(s,t)):Jr(a,t)}});function Jr(e,t){return e.value===void 0&&(e.value=t.defaultValue),e}const Fi=h("$ZodPrefault",(e,t)=>{W.init(e,t),e._zod.optin="optional",V(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(r,n)=>(r.value===void 0&&(r.value=t.defaultValue),t.innerType._zod.run(r,n))}),Vi=h("$ZodNonOptional",(e,t)=>{W.init(e,t),V(e._zod,"values",()=>{const r=t.innerType._zod.values;return r?new Set([...r].filter(n=>n!==void 0)):void 0}),e._zod.parse=(r,n)=>{const a=t.innerType._zod.run(r,n);return a instanceof Promise?a.then(s=>Gr(s,e)):Gr(a,e)}});function Gr(e,t){return!e.issues.length&&e.value===void 0&&e.issues.push({code:"invalid_type",expected:"nonoptional",input:e.value,inst:t}),e}const Bi=h("$ZodCatch",(e,t)=>{W.init(e,t),e._zod.optin="optional",V(e._zod,"optout",()=>t.innerType._zod.optout),V(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(r,n)=>{const a=t.innerType._zod.run(r,n);return a instanceof Promise?a.then(s=>(r.value=s.value,s.issues.length&&(r.value=t.catchValue({...r,error:{issues:s.issues.map(o=>Ie(o,n,ke()))},input:r.value}),r.issues=[]),r)):(r.value=a.value,a.issues.length&&(r.value=t.catchValue({...r,error:{issues:a.issues.map(s=>Ie(s,n,ke()))},input:r.value}),r.issues=[]),r)}}),Ji=h("$ZodPipe",(e,t)=>{W.init(e,t),V(e._zod,"values",()=>t.in._zod.values),V(e._zod,"optin",()=>t.in._zod.optin),V(e._zod,"optout",()=>t.out._zod.optout),e._zod.parse=(r,n)=>{const a=t.in._zod.run(r,n);return a instanceof Promise?a.then(s=>Wr(s,t,n)):Wr(a,t,n)}});function Wr(e,t,r){return Xe(e)?e:t.out._zod.run({value:e.value,issues:e.issues},r)}const Gi=h("$ZodReadonly",(e,t)=>{W.init(e,t),V(e._zod,"propValues",()=>t.innerType._zod.propValues),V(e._zod,"values",()=>t.innerType._zod.values),V(e._zod,"optin",()=>t.innerType._zod.optin),V(e._zod,"optout",()=>t.innerType._zod.optout),e._zod.parse=(r,n)=>{const a=t.innerType._zod.run(r,n);return a instanceof Promise?a.then(qr):qr(a)}});function qr(e){return e.value=Object.freeze(e.value),e}const Wi=h("$ZodLazy",(e,t)=>{W.init(e,t),V(e._zod,"innerType",()=>t.getter()),V(e._zod,"pattern",()=>e._zod.innerType._zod.pattern),V(e._zod,"propValues",()=>e._zod.innerType._zod.propValues),V(e._zod,"optin",()=>e._zod.innerType._zod.optin),V(e._zod,"optout",()=>e._zod.innerType._zod.optout),e._zod.parse=(r,n)=>e._zod.innerType._zod.run(r,n)}),qi=h("$ZodCustom",(e,t)=>{ce.init(e,t),W.init(e,t),e._zod.parse=(r,n)=>r,e._zod.check=r=>{const n=r.value,a=t.fn(n);if(a instanceof Promise)return a.then(s=>Yr(s,r,n,e));Yr(a,r,n,e)}});function Yr(e,t,r,n){if(!e){const a={code:"custom",input:r,inst:n,path:[...n._zod.def.path??[]],continue:!n._zod.def.abort};n._zod.def.params&&(a.params=n._zod.def.params),t.issues.push(tt(a))}}class ha{constructor(){this._map=new Map,this._idmap=new Map}add(t,...r){const n=r[0];if(this._map.set(t,n),n&&typeof n=="object"&&"id"in n){if(this._idmap.has(n.id))throw new Error(`ID ${n.id} already exists in the registry`);this._idmap.set(n.id,t)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(t){const r=this._map.get(t);return r&&typeof r=="object"&&"id"in r&&this._idmap.delete(r.id),this._map.delete(t),this}get(t){const r=t._zod.parent;if(r){const n={...this.get(r)??{}};return delete n.id,{...n,...this._map.get(t)}}return this._map.get(t)}has(t){return this._map.has(t)}}function Yi(){return new ha}const Ye=Yi();function Ki(e,t){return new e({type:"string",...N(t)})}function Hi(e,t){return new e({type:"string",format:"email",check:"string_format",abort:!1,...N(t)})}function Kr(e,t){return new e({type:"string",format:"guid",check:"string_format",abort:!1,...N(t)})}function Xi(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,...N(t)})}function Qi(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...N(t)})}function ec(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...N(t)})}function tc(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...N(t)})}function rc(e,t){return new e({type:"string",format:"url",check:"string_format",abort:!1,...N(t)})}function nc(e,t){return new e({type:"string",format:"emoji",check:"string_format",abort:!1,...N(t)})}function ac(e,t){return new e({type:"string",format:"nanoid",check:"string_format",abort:!1,...N(t)})}function sc(e,t){return new e({type:"string",format:"cuid",check:"string_format",abort:!1,...N(t)})}function oc(e,t){return new e({type:"string",format:"cuid2",check:"string_format",abort:!1,...N(t)})}function ic(e,t){return new e({type:"string",format:"ulid",check:"string_format",abort:!1,...N(t)})}function cc(e,t){return new e({type:"string",format:"xid",check:"string_format",abort:!1,...N(t)})}function uc(e,t){return new e({type:"string",format:"ksuid",check:"string_format",abort:!1,...N(t)})}function lc(e,t){return new e({type:"string",format:"ipv4",check:"string_format",abort:!1,...N(t)})}function dc(e,t){return new e({type:"string",format:"ipv6",check:"string_format",abort:!1,...N(t)})}function fc(e,t){return new e({type:"string",format:"cidrv4",check:"string_format",abort:!1,...N(t)})}function pc(e,t){return new e({type:"string",format:"cidrv6",check:"string_format",abort:!1,...N(t)})}function ma(e,t){return new e({type:"string",format:"base64",check:"string_format",abort:!1,...N(t)})}function hc(e,t){return new e({type:"string",format:"base64url",check:"string_format",abort:!1,...N(t)})}function mc(e,t){return new e({type:"string",format:"e164",check:"string_format",abort:!1,...N(t)})}function gc(e,t){return new e({type:"string",format:"jwt",check:"string_format",abort:!1,...N(t)})}function vc(e,t){return new e({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...N(t)})}function yc(e,t){return new e({type:"string",format:"date",check:"string_format",...N(t)})}function _c(e,t){return new e({type:"string",format:"time",check:"string_format",precision:null,...N(t)})}function bc(e,t){return new e({type:"string",format:"duration",check:"string_format",...N(t)})}function wc(e,t){return new e({type:"number",checks:[],...N(t)})}function xc(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"safeint",...N(t)})}function kc(e,t){return new e({type:"boolean",...N(t)})}function Ic(e,t){return new e({type:"null",...N(t)})}function Sc(e){return new e({type:"unknown"})}function Tc(e,t){return new e({type:"never",...N(t)})}function Hr(e,t){return new ua({check:"less_than",...N(t),value:e,inclusive:!1})}function qt(e,t){return new ua({check:"less_than",...N(t),value:e,inclusive:!0})}function Xr(e,t){return new la({check:"greater_than",...N(t),value:e,inclusive:!1})}function Yt(e,t){return new la({check:"greater_than",...N(t),value:e,inclusive:!0})}function Qr(e,t){return new Uo({check:"multiple_of",...N(t),value:e})}function ga(e,t){return new Vo({check:"max_length",...N(t),maximum:e})}function Tt(e,t){return new Bo({check:"min_length",...N(t),minimum:e})}function va(e,t){return new Jo({check:"length_equals",...N(t),length:e})}function Ec(e,t){return new Go({check:"string_format",format:"regex",...N(t),pattern:e})}function zc(e){return new Wo({check:"string_format",format:"lowercase",...N(e)})}function Ac(e){return new qo({check:"string_format",format:"uppercase",...N(e)})}function $c(e,t){return new Yo({check:"string_format",format:"includes",...N(t),includes:e})}function Zc(e,t){return new Ko({check:"string_format",format:"starts_with",...N(t),prefix:e})}function Oc(e,t){return new Ho({check:"string_format",format:"ends_with",...N(t),suffix:e})}function lt(e){return new Xo({check:"overwrite",tx:e})}function Nc(e){return lt(t=>t.normalize(e))}function Cc(){return lt(e=>e.trim())}function Pc(){return lt(e=>e.toLowerCase())}function Rc(){return lt(e=>e.toUpperCase())}function Mc(e,t,r){return new e({type:"array",element:t,...N(r)})}function jc(e,t,r){const n=N(r);return n.abort??(n.abort=!0),new e({type:"custom",check:"custom",fn:t,...n})}function Dc(e,t,r){return new e({type:"custom",check:"custom",fn:t,...N(r)})}class en{constructor(t){this.counter=0,this.metadataRegistry=(t==null?void 0:t.metadata)??Ye,this.target=(t==null?void 0:t.target)??"draft-2020-12",this.unrepresentable=(t==null?void 0:t.unrepresentable)??"throw",this.override=(t==null?void 0:t.override)??(()=>{}),this.io=(t==null?void 0:t.io)??"output",this.seen=new Map}process(t,r={path:[],schemaPath:[]}){var g,x,A;var n;const a=t._zod.def,s={guid:"uuid",url:"uri",datetime:"date-time",json_string:"json-string",regex:""},o=this.seen.get(t);if(o)return o.count++,r.schemaPath.includes(t)&&(o.cycle=r.path),o.schema;const i={schema:{},count:1,cycle:void 0,path:r.path};this.seen.set(t,i);const u=(x=(g=t._zod).toJSONSchema)==null?void 0:x.call(g);if(u)i.schema=u;else{const b={...r,schemaPath:[...r.schemaPath,t],path:r.path},T=t._zod.parent;if(T)i.ref=T,this.process(T,b),this.seen.get(T).isParent=!0;else{const v=i.schema;switch(a.type){case"string":{const l=v;l.type="string";const{minimum:m,maximum:p,format:f,patterns:S,contentEncoding:_}=t._zod.bag;if(typeof m=="number"&&(l.minLength=m),typeof p=="number"&&(l.maxLength=p),f&&(l.format=s[f]??f,l.format===""&&delete l.format),_&&(l.contentEncoding=_),S&&S.size>0){const I=[...S];I.length===1?l.pattern=I[0].source:I.length>1&&(i.schema.allOf=[...I.map(J=>({...this.target==="draft-7"?{type:"string"}:{},pattern:J.source}))])}break}case"number":{const l=v,{minimum:m,maximum:p,format:f,multipleOf:S,exclusiveMaximum:_,exclusiveMinimum:I}=t._zod.bag;typeof f=="string"&&f.includes("int")?l.type="integer":l.type="number",typeof I=="number"&&(l.exclusiveMinimum=I),typeof m=="number"&&(l.minimum=m,typeof I=="number"&&(I>=m?delete l.minimum:delete l.exclusiveMinimum)),typeof _=="number"&&(l.exclusiveMaximum=_),typeof p=="number"&&(l.maximum=p,typeof _=="number"&&(_<=p?delete l.maximum:delete l.exclusiveMaximum)),typeof S=="number"&&(l.multipleOf=S);break}case"boolean":{const l=v;l.type="boolean";break}case"bigint":{if(this.unrepresentable==="throw")throw new Error("BigInt cannot be represented in JSON Schema");break}case"symbol":{if(this.unrepresentable==="throw")throw new Error("Symbols cannot be represented in JSON Schema");break}case"null":{v.type="null";break}case"any":break;case"unknown":break;case"undefined":{if(this.unrepresentable==="throw")throw new Error("Undefined cannot be represented in JSON Schema");break}case"void":{if(this.unrepresentable==="throw")throw new Error("Void cannot be represented in JSON Schema");break}case"never":{v.not={};break}case"date":{if(this.unrepresentable==="throw")throw new Error("Date cannot be represented in JSON Schema");break}case"array":{const l=v,{minimum:m,maximum:p}=t._zod.bag;typeof m=="number"&&(l.minItems=m),typeof p=="number"&&(l.maxItems=p),l.type="array",l.items=this.process(a.element,{...b,path:[...b.path,"items"]});break}case"object":{const l=v;l.type="object",l.properties={};const m=a.shape;for(const S in m)l.properties[S]=this.process(m[S],{...b,path:[...b.path,"properties",S]});const p=new Set(Object.keys(m)),f=new Set([...p].filter(S=>{const _=a.shape[S]._zod;return this.io==="input"?_.optin===void 0:_.optout===void 0}));f.size>0&&(l.required=Array.from(f)),((A=a.catchall)==null?void 0:A._zod.def.type)==="never"?l.additionalProperties=!1:a.catchall?a.catchall&&(l.additionalProperties=this.process(a.catchall,{...b,path:[...b.path,"additionalProperties"]})):this.io==="output"&&(l.additionalProperties=!1);break}case"union":{const l=v;l.anyOf=a.options.map((m,p)=>this.process(m,{...b,path:[...b.path,"anyOf",p]}));break}case"intersection":{const l=v,m=this.process(a.left,{...b,path:[...b.path,"allOf",0]}),p=this.process(a.right,{...b,path:[...b.path,"allOf",1]}),f=_=>"allOf"in _&&Object.keys(_).length===1,S=[...f(m)?m.allOf:[m],...f(p)?p.allOf:[p]];l.allOf=S;break}case"tuple":{const l=v;l.type="array";const m=a.items.map((S,_)=>this.process(S,{...b,path:[...b.path,"prefixItems",_]}));if(this.target==="draft-2020-12"?l.prefixItems=m:l.items=m,a.rest){const S=this.process(a.rest,{...b,path:[...b.path,"items"]});this.target==="draft-2020-12"?l.items=S:l.additionalItems=S}a.rest&&(l.items=this.process(a.rest,{...b,path:[...b.path,"items"]}));const{minimum:p,maximum:f}=t._zod.bag;typeof p=="number"&&(l.minItems=p),typeof f=="number"&&(l.maxItems=f);break}case"record":{const l=v;l.type="object",l.propertyNames=this.process(a.keyType,{...b,path:[...b.path,"propertyNames"]}),l.additionalProperties=this.process(a.valueType,{...b,path:[...b.path,"additionalProperties"]});break}case"map":{if(this.unrepresentable==="throw")throw new Error("Map cannot be represented in JSON Schema");break}case"set":{if(this.unrepresentable==="throw")throw new Error("Set cannot be represented in JSON Schema");break}case"enum":{const l=v,m=Xn(a.entries);m.every(p=>typeof p=="number")&&(l.type="number"),m.every(p=>typeof p=="string")&&(l.type="string"),l.enum=m;break}case"literal":{const l=v,m=[];for(const p of a.values)if(p===void 0){if(this.unrepresentable==="throw")throw new Error("Literal `undefined` cannot be represented in JSON Schema")}else if(typeof p=="bigint"){if(this.unrepresentable==="throw")throw new Error("BigInt literals cannot be represented in JSON Schema");m.push(Number(p))}else m.push(p);if(m.length!==0)if(m.length===1){const p=m[0];l.type=p===null?"null":typeof p,l.const=p}else m.every(p=>typeof p=="number")&&(l.type="number"),m.every(p=>typeof p=="string")&&(l.type="string"),m.every(p=>typeof p=="boolean")&&(l.type="string"),m.every(p=>p===null)&&(l.type="null"),l.enum=m;break}case"file":{const l=v,m={type:"string",format:"binary",contentEncoding:"binary"},{minimum:p,maximum:f,mime:S}=t._zod.bag;p!==void 0&&(m.minLength=p),f!==void 0&&(m.maxLength=f),S?S.length===1?(m.contentMediaType=S[0],Object.assign(l,m)):l.anyOf=S.map(_=>({...m,contentMediaType:_})):Object.assign(l,m);break}case"transform":{if(this.unrepresentable==="throw")throw new Error("Transforms cannot be represented in JSON Schema");break}case"nullable":{const l=this.process(a.innerType,b);v.anyOf=[l,{type:"null"}];break}case"nonoptional":{this.process(a.innerType,b),i.ref=a.innerType;break}case"success":{const l=v;l.type="boolean";break}case"default":{this.process(a.innerType,b),i.ref=a.innerType,v.default=JSON.parse(JSON.stringify(a.defaultValue));break}case"prefault":{this.process(a.innerType,b),i.ref=a.innerType,this.io==="input"&&(v._prefault=JSON.parse(JSON.stringify(a.defaultValue)));break}case"catch":{this.process(a.innerType,b),i.ref=a.innerType;let l;try{l=a.catchValue(void 0)}catch{throw new Error("Dynamic catch values are not supported in JSON Schema")}v.default=l;break}case"nan":{if(this.unrepresentable==="throw")throw new Error("NaN cannot be represented in JSON Schema");break}case"template_literal":{const l=v,m=t._zod.pattern;if(!m)throw new Error("Pattern not found in template literal");l.type="string",l.pattern=m.source;break}case"pipe":{const l=this.io==="input"?a.in._zod.def.type==="transform"?a.out:a.in:a.out;this.process(l,b),i.ref=l;break}case"readonly":{this.process(a.innerType,b),i.ref=a.innerType,v.readOnly=!0;break}case"promise":{this.process(a.innerType,b),i.ref=a.innerType;break}case"optional":{this.process(a.innerType,b),i.ref=a.innerType;break}case"lazy":{const l=t._zod.innerType;this.process(l,b),i.ref=l;break}case"custom":{if(this.unrepresentable==="throw")throw new Error("Custom types cannot be represented in JSON Schema");break}}}}const c=this.metadataRegistry.get(t);return c&&Object.assign(i.schema,c),this.io==="input"&&te(t)&&(delete i.schema.examples,delete i.schema.default),this.io==="input"&&i.schema._prefault&&((n=i.schema).default??(n.default=i.schema._prefault)),delete i.schema._prefault,this.seen.get(t).schema}emit(t,r){var d,g,x,A,b,T;const n={cycles:(r==null?void 0:r.cycles)??"ref",reused:(r==null?void 0:r.reused)??"inline",external:(r==null?void 0:r.external)??void 0},a=this.seen.get(t);if(!a)throw new Error("Unprocessed schema. This is a bug in Zod.");const s=v=>{var S;const l=this.target==="draft-2020-12"?"$defs":"definitions";if(n.external){const _=(S=n.external.registry.get(v[0]))==null?void 0:S.id,I=n.external.uri??(de=>de);if(_)return{ref:I(_)};const J=v[1].defId??v[1].schema.id??`schema${this.counter++}`;return v[1].defId=J,{defId:J,ref:`${I("__shared")}#/${l}/${J}`}}if(v[1]===a)return{ref:"#"};const p=`#/${l}/`,f=v[1].schema.id??`__schema${this.counter++}`;return{defId:f,ref:p+f}},o=v=>{if(v[1].schema.$ref)return;const l=v[1],{ref:m,defId:p}=s(v);l.def={...l.schema},p&&(l.defId=p);const f=l.schema;for(const S in f)delete f[S];f.$ref=m};if(n.cycles==="throw")for(const v of this.seen.entries()){const l=v[1];if(l.cycle)throw new Error(`Cycle detected: #/${(d=l.cycle)==null?void 0:d.join("/")}/<root>

Set the \`cycles\` parameter to \`"ref"\` to resolve cyclical schemas with defs.`)}for(const v of this.seen.entries()){const l=v[1];if(t===v[0]){o(v);continue}if(n.external){const p=(g=n.external.registry.get(v[0]))==null?void 0:g.id;if(t!==v[0]&&p){o(v);continue}}if((x=this.metadataRegistry.get(v[0]))==null?void 0:x.id){o(v);continue}if(l.cycle){o(v);continue}if(l.count>1&&n.reused==="ref"){o(v);continue}}const i=(v,l)=>{const m=this.seen.get(v),p=m.def??m.schema,f={...p};if(m.ref===null)return;const S=m.ref;if(m.ref=null,S){i(S,l);const _=this.seen.get(S).schema;_.$ref&&l.target==="draft-7"?(p.allOf=p.allOf??[],p.allOf.push(_)):(Object.assign(p,_),Object.assign(p,f))}m.isParent||this.override({zodSchema:v,jsonSchema:p,path:m.path??[]})};for(const v of[...this.seen.entries()].reverse())i(v[0],{target:this.target});const u={};if(this.target==="draft-2020-12"?u.$schema="https://json-schema.org/draft/2020-12/schema":this.target==="draft-7"?u.$schema="http://json-schema.org/draft-07/schema#":console.warn(`Invalid target: ${this.target}`),(A=n.external)!=null&&A.uri){const v=(b=n.external.registry.get(t))==null?void 0:b.id;if(!v)throw new Error("Schema is missing an `id` property");u.$id=n.external.uri(v)}Object.assign(u,a.def);const c=((T=n.external)==null?void 0:T.defs)??{};for(const v of this.seen.entries()){const l=v[1];l.def&&l.defId&&(c[l.defId]=l.def)}n.external||Object.keys(c).length>0&&(this.target==="draft-2020-12"?u.$defs=c:u.definitions=c);try{return JSON.parse(JSON.stringify(u))}catch{throw new Error("Error converting schema to JSON.")}}}function Lc(e,t){if(e instanceof ha){const n=new en(t),a={};for(const i of e._idmap.entries()){const[u,c]=i;n.process(c)}const s={},o={registry:e,uri:t==null?void 0:t.uri,defs:a};for(const i of e._idmap.entries()){const[u,c]=i;s[u]=n.emit(c,{...t,external:o})}if(Object.keys(a).length>0){const i=n.target==="draft-2020-12"?"$defs":"definitions";s.__shared={[i]:a}}return{schemas:s}}const r=new en(t);return r.process(e),r.emit(e,t)}function te(e,t){const r=t??{seen:new Set};if(r.seen.has(e))return!1;r.seen.add(e);const a=e._zod.def;switch(a.type){case"string":case"number":case"bigint":case"boolean":case"date":case"symbol":case"undefined":case"null":case"any":case"unknown":case"never":case"void":case"literal":case"enum":case"nan":case"file":case"template_literal":return!1;case"array":return te(a.element,r);case"object":{for(const s in a.shape)if(te(a.shape[s],r))return!0;return!1}case"union":{for(const s of a.options)if(te(s,r))return!0;return!1}case"intersection":return te(a.left,r)||te(a.right,r);case"tuple":{for(const s of a.items)if(te(s,r))return!0;return!!(a.rest&&te(a.rest,r))}case"record":return te(a.keyType,r)||te(a.valueType,r);case"map":return te(a.keyType,r)||te(a.valueType,r);case"set":return te(a.valueType,r);case"promise":case"optional":case"nonoptional":case"nullable":case"readonly":return te(a.innerType,r);case"lazy":return te(a.getter(),r);case"default":return te(a.innerType,r);case"prefault":return te(a.innerType,r);case"custom":return!1;case"transform":return!0;case"pipe":return te(a.in,r)||te(a.out,r);case"success":return!1;case"catch":return!1}throw new Error(`Unknown schema type: ${a.type}`)}const Uc=h("ZodISODateTime",(e,t)=>{fi.init(e,t),X.init(e,t)});function Fc(e){return vc(Uc,e)}const Vc=h("ZodISODate",(e,t)=>{pi.init(e,t),X.init(e,t)});function Bc(e){return yc(Vc,e)}const Jc=h("ZodISOTime",(e,t)=>{hi.init(e,t),X.init(e,t)});function Gc(e){return _c(Jc,e)}const Wc=h("ZodISODuration",(e,t)=>{mi.init(e,t),X.init(e,t)});function qc(e){return bc(Wc,e)}const Yc=(e,t)=>{ta.init(e,t),e.name="ZodError",Object.defineProperties(e,{format:{value:r=>io(e,r)},flatten:{value:r=>oo(e,r)},addIssue:{value:r=>e.issues.push(r)},addIssues:{value:r=>e.issues.push(...r)},isEmpty:{get(){return e.issues.length===0}}})},Mt=h("ZodError",Yc,{Parent:Error}),Kc=co(Mt),Hc=uo(Mt),Xc=na(Mt),ya=aa(Mt),H=h("ZodType",(e,t)=>(W.init(e,t),e.def=t,Object.defineProperty(e,"_def",{value:t}),e.check=(...r)=>e.clone({...t,checks:[...t.checks??[],...r.map(n=>typeof n=="function"?{_zod:{check:n,def:{check:"custom"},onattach:[]}}:n)]}),e.clone=(r,n)=>Ce(e,r,n),e.brand=()=>e,e.register=(r,n)=>(r.add(e,n),e),e.parse=(r,n)=>Kc(e,r,n,{callee:e.parse}),e.safeParse=(r,n)=>Xc(e,r,n),e.parseAsync=async(r,n)=>Hc(e,r,n,{callee:e.parseAsync}),e.safeParseAsync=async(r,n)=>ya(e,r,n),e.spa=e.safeParseAsync,e.refine=(r,n)=>e.check(Gu(r,n)),e.superRefine=r=>e.check(Wu(r)),e.overwrite=r=>e.check(lt(r)),e.optional=()=>ee(e),e.nullable=()=>nn(e),e.nullish=()=>ee(nn(e)),e.nonoptional=r=>Ru(e,r),e.array=()=>_e(e),e.or=r=>ne([e,r]),e.and=r=>Tu(e,r),e.transform=r=>an(e,$u(r)),e.default=r=>Nu(e,r),e.prefault=r=>Pu(e,r),e.catch=r=>ju(e,r),e.pipe=r=>an(e,r),e.readonly=()=>Uu(e),e.describe=r=>{const n=e.clone();return Ye.add(n,{description:r}),n},Object.defineProperty(e,"description",{get(){var r;return(r=Ye.get(e))==null?void 0:r.description},configurable:!0}),e.meta=(...r)=>{if(r.length===0)return Ye.get(e);const n=e.clone();return Ye.add(n,r[0]),n},e.isOptional=()=>e.safeParse(void 0).success,e.isNullable=()=>e.safeParse(null).success,e)),_a=h("_ZodString",(e,t)=>{_r.init(e,t),H.init(e,t);const r=e._zod.bag;e.format=r.format??null,e.minLength=r.minimum??null,e.maxLength=r.maximum??null,e.regex=(...n)=>e.check(Ec(...n)),e.includes=(...n)=>e.check($c(...n)),e.startsWith=(...n)=>e.check(Zc(...n)),e.endsWith=(...n)=>e.check(Oc(...n)),e.min=(...n)=>e.check(Tt(...n)),e.max=(...n)=>e.check(ga(...n)),e.length=(...n)=>e.check(va(...n)),e.nonempty=(...n)=>e.check(Tt(1,...n)),e.lowercase=n=>e.check(zc(n)),e.uppercase=n=>e.check(Ac(n)),e.trim=()=>e.check(Cc()),e.normalize=(...n)=>e.check(Nc(...n)),e.toLowerCase=()=>e.check(Pc()),e.toUpperCase=()=>e.check(Rc())}),Qc=h("ZodString",(e,t)=>{_r.init(e,t),_a.init(e,t),e.email=r=>e.check(Hi(eu,r)),e.url=r=>e.check(rc(tu,r)),e.jwt=r=>e.check(gc(mu,r)),e.emoji=r=>e.check(nc(ru,r)),e.guid=r=>e.check(Kr(tn,r)),e.uuid=r=>e.check(Xi(_t,r)),e.uuidv4=r=>e.check(Qi(_t,r)),e.uuidv6=r=>e.check(ec(_t,r)),e.uuidv7=r=>e.check(tc(_t,r)),e.nanoid=r=>e.check(ac(nu,r)),e.guid=r=>e.check(Kr(tn,r)),e.cuid=r=>e.check(sc(au,r)),e.cuid2=r=>e.check(oc(su,r)),e.ulid=r=>e.check(ic(ou,r)),e.base64=r=>e.check(ma(ba,r)),e.base64url=r=>e.check(hc(pu,r)),e.xid=r=>e.check(cc(iu,r)),e.ksuid=r=>e.check(uc(cu,r)),e.ipv4=r=>e.check(lc(uu,r)),e.ipv6=r=>e.check(dc(lu,r)),e.cidrv4=r=>e.check(fc(du,r)),e.cidrv6=r=>e.check(pc(fu,r)),e.e164=r=>e.check(mc(hu,r)),e.datetime=r=>e.check(Fc(r)),e.date=r=>e.check(Bc(r)),e.time=r=>e.check(Gc(r)),e.duration=r=>e.check(qc(r))});function y(e){return Ki(Qc,e)}const X=h("ZodStringFormat",(e,t)=>{K.init(e,t),_a.init(e,t)}),eu=h("ZodEmail",(e,t)=>{ni.init(e,t),X.init(e,t)}),tn=h("ZodGUID",(e,t)=>{ti.init(e,t),X.init(e,t)}),_t=h("ZodUUID",(e,t)=>{ri.init(e,t),X.init(e,t)}),tu=h("ZodURL",(e,t)=>{ai.init(e,t),X.init(e,t)}),ru=h("ZodEmoji",(e,t)=>{si.init(e,t),X.init(e,t)}),nu=h("ZodNanoID",(e,t)=>{oi.init(e,t),X.init(e,t)}),au=h("ZodCUID",(e,t)=>{ii.init(e,t),X.init(e,t)}),su=h("ZodCUID2",(e,t)=>{ci.init(e,t),X.init(e,t)}),ou=h("ZodULID",(e,t)=>{ui.init(e,t),X.init(e,t)}),iu=h("ZodXID",(e,t)=>{li.init(e,t),X.init(e,t)}),cu=h("ZodKSUID",(e,t)=>{di.init(e,t),X.init(e,t)}),uu=h("ZodIPv4",(e,t)=>{gi.init(e,t),X.init(e,t)}),lu=h("ZodIPv6",(e,t)=>{vi.init(e,t),X.init(e,t)}),du=h("ZodCIDRv4",(e,t)=>{yi.init(e,t),X.init(e,t)}),fu=h("ZodCIDRv6",(e,t)=>{_i.init(e,t),X.init(e,t)}),ba=h("ZodBase64",(e,t)=>{bi.init(e,t),X.init(e,t)});function wa(e){return ma(ba,e)}const pu=h("ZodBase64URL",(e,t)=>{xi.init(e,t),X.init(e,t)}),hu=h("ZodE164",(e,t)=>{ki.init(e,t),X.init(e,t)}),mu=h("ZodJWT",(e,t)=>{Si.init(e,t),X.init(e,t)}),xa=h("ZodNumber",(e,t)=>{fa.init(e,t),H.init(e,t),e.gt=(n,a)=>e.check(Xr(n,a)),e.gte=(n,a)=>e.check(Yt(n,a)),e.min=(n,a)=>e.check(Yt(n,a)),e.lt=(n,a)=>e.check(Hr(n,a)),e.lte=(n,a)=>e.check(qt(n,a)),e.max=(n,a)=>e.check(qt(n,a)),e.int=n=>e.check(rn(n)),e.safe=n=>e.check(rn(n)),e.positive=n=>e.check(Xr(0,n)),e.nonnegative=n=>e.check(Yt(0,n)),e.negative=n=>e.check(Hr(0,n)),e.nonpositive=n=>e.check(qt(0,n)),e.multipleOf=(n,a)=>e.check(Qr(n,a)),e.step=(n,a)=>e.check(Qr(n,a)),e.finite=()=>e;const r=e._zod.bag;e.minValue=Math.max(r.minimum??Number.NEGATIVE_INFINITY,r.exclusiveMinimum??Number.NEGATIVE_INFINITY)??null,e.maxValue=Math.min(r.maximum??Number.POSITIVE_INFINITY,r.exclusiveMaximum??Number.POSITIVE_INFINITY)??null,e.isInt=(r.format??"").includes("int")||Number.isSafeInteger(r.multipleOf??.5),e.isFinite=!0,e.format=r.format??null});function rt(e){return wc(xa,e)}const gu=h("ZodNumberFormat",(e,t)=>{Ti.init(e,t),xa.init(e,t)});function rn(e){return xc(gu,e)}const vu=h("ZodBoolean",(e,t)=>{Ei.init(e,t),H.init(e,t)});function Q(e){return kc(vu,e)}const yu=h("ZodNull",(e,t)=>{zi.init(e,t),H.init(e,t)});function _u(e){return Ic(yu,e)}const bu=h("ZodUnknown",(e,t)=>{Ai.init(e,t),H.init(e,t)});function Y(){return Sc(bu)}const wu=h("ZodNever",(e,t)=>{$i.init(e,t),H.init(e,t)});function ie(e){return Tc(wu,e)}const xu=h("ZodArray",(e,t)=>{Zi.init(e,t),H.init(e,t),e.element=t.element,e.min=(r,n)=>e.check(Tt(r,n)),e.nonempty=r=>e.check(Tt(1,r)),e.max=(r,n)=>e.check(ga(r,n)),e.length=(r,n)=>e.check(va(r,n)),e.unwrap=()=>e.element});function _e(e,t){return Mc(xu,e,t)}const br=h("ZodObject",(e,t)=>{Oi.init(e,t),H.init(e,t),V(e,"shape",()=>t.shape),e.keyof=()=>jt(Object.keys(e._zod.def.shape)),e.catchall=r=>e.clone({...e._zod.def,catchall:r}),e.passthrough=()=>e.clone({...e._zod.def,catchall:Y()}),e.loose=()=>e.clone({...e._zod.def,catchall:Y()}),e.strict=()=>e.clone({...e._zod.def,catchall:ie()}),e.strip=()=>e.clone({...e._zod.def,catchall:void 0}),e.extend=r=>ro(e,r),e.merge=r=>no(e,r),e.pick=r=>eo(e,r),e.omit=r=>to(e,r),e.partial=(...r)=>ao(Ia,e,r[0]),e.required=(...r)=>so(Sa,e,r[0])});function P(e,t){const r={type:"object",get shape(){return ct(this,"shape",{...e}),this.shape},...N(t)};return new br(r)}function q(e,t){return new br({type:"object",get shape(){return ct(this,"shape",{...e}),this.shape},catchall:ie(),...N(t)})}function De(e,t){return new br({type:"object",get shape(){return ct(this,"shape",{...e}),this.shape},catchall:Y(),...N(t)})}const ka=h("ZodUnion",(e,t)=>{pa.init(e,t),H.init(e,t),e.options=t.options});function ne(e,t){return new ka({type:"union",options:e,...N(t)})}const ku=h("ZodDiscriminatedUnion",(e,t)=>{ka.init(e,t),Ni.init(e,t)});function Iu(e,t,r){return new ku({type:"union",options:t,discriminator:e,...N(r)})}const Su=h("ZodIntersection",(e,t)=>{Ci.init(e,t),H.init(e,t)});function Tu(e,t){return new Su({type:"intersection",left:e,right:t})}const Eu=h("ZodRecord",(e,t)=>{Pi.init(e,t),H.init(e,t),e.keyType=t.keyType,e.valueType=t.valueType});function rr(e,t,r){return new Eu({type:"record",keyType:e,valueType:t,...N(r)})}const nr=h("ZodEnum",(e,t)=>{Ri.init(e,t),H.init(e,t),e.enum=t.entries,e.options=Object.values(t.entries);const r=new Set(Object.keys(t.entries));e.extract=(n,a)=>{const s={};for(const o of n)if(r.has(o))s[o]=t.entries[o];else throw new Error(`Key ${o} not found in enum`);return new nr({...t,checks:[],...N(a),entries:s})},e.exclude=(n,a)=>{const s={...t.entries};for(const o of n)if(r.has(o))delete s[o];else throw new Error(`Key ${o} not found in enum`);return new nr({...t,checks:[],...N(a),entries:s})}});function jt(e,t){const r=Array.isArray(e)?Object.fromEntries(e.map(n=>[n,n])):e;return new nr({type:"enum",entries:r,...N(t)})}const zu=h("ZodLiteral",(e,t)=>{Mi.init(e,t),H.init(e,t),e.values=new Set(t.values),Object.defineProperty(e,"value",{get(){if(t.values.length>1)throw new Error("This schema contains multiple valid literal values. Use `.values` instead.");return t.values[0]}})});function z(e,t){return new zu({type:"literal",values:Array.isArray(e)?e:[e],...N(t)})}const Au=h("ZodTransform",(e,t)=>{ji.init(e,t),H.init(e,t),e._zod.parse=(r,n)=>{r.addIssue=s=>{if(typeof s=="string")r.issues.push(tt(s,r.value,t));else{const o=s;o.fatal&&(o.continue=!1),o.code??(o.code="custom"),o.input??(o.input=r.value),o.inst??(o.inst=e),o.continue??(o.continue=!0),r.issues.push(tt(o))}};const a=t.transform(r.value,r);return a instanceof Promise?a.then(s=>(r.value=s,r)):(r.value=a,r)}});function $u(e){return new Au({type:"transform",transform:e})}const Ia=h("ZodOptional",(e,t)=>{Di.init(e,t),H.init(e,t),e.unwrap=()=>e._zod.def.innerType});function ee(e){return new Ia({type:"optional",innerType:e})}const Zu=h("ZodNullable",(e,t)=>{Li.init(e,t),H.init(e,t),e.unwrap=()=>e._zod.def.innerType});function nn(e){return new Zu({type:"nullable",innerType:e})}const Ou=h("ZodDefault",(e,t)=>{Ui.init(e,t),H.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeDefault=e.unwrap});function Nu(e,t){return new Ou({type:"default",innerType:e,get defaultValue(){return typeof t=="function"?t():t}})}const Cu=h("ZodPrefault",(e,t)=>{Fi.init(e,t),H.init(e,t),e.unwrap=()=>e._zod.def.innerType});function Pu(e,t){return new Cu({type:"prefault",innerType:e,get defaultValue(){return typeof t=="function"?t():t}})}const Sa=h("ZodNonOptional",(e,t)=>{Vi.init(e,t),H.init(e,t),e.unwrap=()=>e._zod.def.innerType});function Ru(e,t){return new Sa({type:"nonoptional",innerType:e,...N(t)})}const Mu=h("ZodCatch",(e,t)=>{Bi.init(e,t),H.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeCatch=e.unwrap});function ju(e,t){return new Mu({type:"catch",innerType:e,catchValue:typeof t=="function"?t:()=>t})}const Du=h("ZodPipe",(e,t)=>{Ji.init(e,t),H.init(e,t),e.in=t.in,e.out=t.out});function an(e,t){return new Du({type:"pipe",in:e,out:t})}const Lu=h("ZodReadonly",(e,t)=>{Gi.init(e,t),H.init(e,t)});function Uu(e){return new Lu({type:"readonly",innerType:e})}const Fu=h("ZodLazy",(e,t)=>{Wi.init(e,t),H.init(e,t),e.unwrap=()=>e._zod.def.getter()});function Vu(e){return new Fu({type:"lazy",getter:e})}const wr=h("ZodCustom",(e,t)=>{qi.init(e,t),H.init(e,t)});function Bu(e){const t=new ce({check:"custom"});return t._zod.check=e,t}function Ju(e,t){return jc(wr,e??(()=>!0),t)}function Gu(e,t={}){return Dc(wr,e,t)}function Wu(e){const t=Bu(r=>(r.addIssue=n=>{if(typeof n=="string")r.issues.push(tt(n,r.value,t._zod.def));else{const a=n;a.fatal&&(a.continue=!1),a.code??(a.code="custom"),a.input??(a.input=r.value),a.inst??(a.inst=t),a.continue??(a.continue=!t._zod.def.abort),r.issues.push(tt(a))}},e(r.value,r)));return t}function Et(e,t={error:`Input not instance of ${e.name}`}){const r=new wr({type:"custom",check:"custom",fn:n=>n instanceof e,abort:!0,...N(t)});return r._zod.bag.Class=e,r}const qu=Symbol("Let zodToJsonSchema decide on which parser to use"),sn={name:void 0,$refStrategy:"root",basePath:["#"],effectStrategy:"input",pipeStrategy:"all",dateStrategy:"format:date-time",mapStrategy:"entries",removeAdditionalStrategy:"passthrough",definitionPath:"definitions",target:"jsonSchema7",strictUnions:!1,definitions:{},errorMessages:!1,markdownDescription:!1,patternStrategy:"escape",applyRegexFlags:!1,emailStrategy:"format:email",base64Strategy:"contentEncoding:base64",nameStrategy:"ref"},Yu=e=>typeof e=="string"?{...sn,name:e}:{...sn,...e},Ku=e=>{const t=Yu(e),r=t.name!==void 0?[...t.basePath,t.definitionPath,t.name]:t.basePath;return{...t,currentPath:r,propertyPath:void 0,seen:new Map(Object.entries(t.definitions).map(([n,a])=>[a._def,{def:a._def,path:[...t.basePath,t.definitionPath,n],jsonSchema:void 0}]))}};function Ta(e,t,r,n){n!=null&&n.errorMessages&&r&&(e.errorMessage={...e.errorMessage,[t]:r})}function B(e,t,r,n,a){e[t]=r,Ta(e,t,n,a)}var U;(function(e){e.assertEqual=a=>{};function t(a){}e.assertIs=t;function r(a){throw new Error}e.assertNever=r,e.arrayToEnum=a=>{const s={};for(const o of a)s[o]=o;return s},e.getValidEnumValues=a=>{const s=e.objectKeys(a).filter(i=>typeof a[a[i]]!="number"),o={};for(const i of s)o[i]=a[i];return e.objectValues(o)},e.objectValues=a=>e.objectKeys(a).map(function(s){return a[s]}),e.objectKeys=typeof Object.keys=="function"?a=>Object.keys(a):a=>{const s=[];for(const o in a)Object.prototype.hasOwnProperty.call(a,o)&&s.push(o);return s},e.find=(a,s)=>{for(const o of a)if(s(o))return o},e.isInteger=typeof Number.isInteger=="function"?a=>Number.isInteger(a):a=>typeof a=="number"&&Number.isFinite(a)&&Math.floor(a)===a;function n(a,s=" | "){return a.map(o=>typeof o=="string"?`'${o}'`:o).join(s)}e.joinValues=n,e.jsonStringifyReplacer=(a,s)=>typeof s=="bigint"?s.toString():s})(U||(U={}));var on;(function(e){e.mergeShapes=(t,r)=>({...t,...r})})(on||(on={}));const O=U.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),we=e=>{switch(typeof e){case"undefined":return O.undefined;case"string":return O.string;case"number":return Number.isNaN(e)?O.nan:O.number;case"boolean":return O.boolean;case"function":return O.function;case"bigint":return O.bigint;case"symbol":return O.symbol;case"object":return Array.isArray(e)?O.array:e===null?O.null:e.then&&typeof e.then=="function"&&e.catch&&typeof e.catch=="function"?O.promise:typeof Map<"u"&&e instanceof Map?O.map:typeof Set<"u"&&e instanceof Set?O.set:typeof Date<"u"&&e instanceof Date?O.date:O.object;default:return O.unknown}},w=U.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class ye extends Error{get errors(){return this.issues}constructor(t){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const r=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,r):this.__proto__=r,this.name="ZodError",this.issues=t}format(t){const r=t||function(s){return s.message},n={_errors:[]},a=s=>{for(const o of s.issues)if(o.code==="invalid_union")o.unionErrors.map(a);else if(o.code==="invalid_return_type")a(o.returnTypeError);else if(o.code==="invalid_arguments")a(o.argumentsError);else if(o.path.length===0)n._errors.push(r(o));else{let i=n,u=0;for(;u<o.path.length;){const c=o.path[u];u===o.path.length-1?(i[c]=i[c]||{_errors:[]},i[c]._errors.push(r(o))):i[c]=i[c]||{_errors:[]},i=i[c],u++}}};return a(this),n}static assert(t){if(!(t instanceof ye))throw new Error(`Not a ZodError: ${t}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,U.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(t=r=>r.message){const r={},n=[];for(const a of this.issues)if(a.path.length>0){const s=a.path[0];r[s]=r[s]||[],r[s].push(t(a))}else n.push(t(a));return{formErrors:n,fieldErrors:r}}get formErrors(){return this.flatten()}}ye.create=e=>new ye(e);const ar=(e,t)=>{let r;switch(e.code){case w.invalid_type:e.received===O.undefined?r="Required":r=`Expected ${e.expected}, received ${e.received}`;break;case w.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(e.expected,U.jsonStringifyReplacer)}`;break;case w.unrecognized_keys:r=`Unrecognized key(s) in object: ${U.joinValues(e.keys,", ")}`;break;case w.invalid_union:r="Invalid input";break;case w.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${U.joinValues(e.options)}`;break;case w.invalid_enum_value:r=`Invalid enum value. Expected ${U.joinValues(e.options)}, received '${e.received}'`;break;case w.invalid_arguments:r="Invalid function arguments";break;case w.invalid_return_type:r="Invalid function return type";break;case w.invalid_date:r="Invalid date";break;case w.invalid_string:typeof e.validation=="object"?"includes"in e.validation?(r=`Invalid input: must include "${e.validation.includes}"`,typeof e.validation.position=="number"&&(r=`${r} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?r=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?r=`Invalid input: must end with "${e.validation.endsWith}"`:U.assertNever(e.validation):e.validation!=="regex"?r=`Invalid ${e.validation}`:r="Invalid";break;case w.too_small:e.type==="array"?r=`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:e.type==="string"?r=`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:e.type==="number"?r=`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:e.type==="bigint"?r=`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:e.type==="date"?r=`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:r="Invalid input";break;case w.too_big:e.type==="array"?r=`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:e.type==="string"?r=`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:e.type==="number"?r=`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:e.type==="bigint"?r=`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:e.type==="date"?r=`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:r="Invalid input";break;case w.custom:r="Invalid input";break;case w.invalid_intersection_types:r="Intersection results could not be merged";break;case w.not_multiple_of:r=`Number must be a multiple of ${e.multipleOf}`;break;case w.not_finite:r="Number must be finite";break;default:r=t.defaultError,U.assertNever(e)}return{message:r}};let Hu=ar;function Xu(){return Hu}const Qu=e=>{const{data:t,path:r,errorMaps:n,issueData:a}=e,s=[...r,...a.path||[]],o={...a,path:s};if(a.message!==void 0)return{...a,path:s,message:a.message};let i="";const u=n.filter(c=>!!c).slice().reverse();for(const c of u)i=c(o,{data:t,defaultError:i}).message;return{...a,path:s,message:i}};function E(e,t){const r=Xu(),n=Qu({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,r,r===ar?void 0:ar].filter(a=>!!a)});e.common.issues.push(n)}class le{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(t,r){const n=[];for(const a of r){if(a.status==="aborted")return R;a.status==="dirty"&&t.dirty(),n.push(a.value)}return{status:t.value,value:n}}static async mergeObjectAsync(t,r){const n=[];for(const a of r){const s=await a.key,o=await a.value;n.push({key:s,value:o})}return le.mergeObjectSync(t,n)}static mergeObjectSync(t,r){const n={};for(const a of r){const{key:s,value:o}=a;if(s.status==="aborted"||o.status==="aborted")return R;s.status==="dirty"&&t.dirty(),o.status==="dirty"&&t.dirty(),s.value!=="__proto__"&&(typeof o.value<"u"||a.alwaysSet)&&(n[s.value]=o.value)}return{status:t.value,value:n}}}const R=Object.freeze({status:"aborted"}),Ke=e=>({status:"dirty",value:e}),fe=e=>({status:"valid",value:e}),cn=e=>e.status==="aborted",un=e=>e.status==="dirty",Le=e=>e.status==="valid",zt=e=>typeof Promise<"u"&&e instanceof Promise;var C;(function(e){e.errToObj=t=>typeof t=="string"?{message:t}:t||{},e.toString=t=>typeof t=="string"?t:t==null?void 0:t.message})(C||(C={}));class Se{constructor(t,r,n,a){this._cachedPath=[],this.parent=t,this.data=r,this._path=n,this._key=a}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const ln=(e,t)=>{if(Le(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const r=new ye(e.common.issues);return this._error=r,this._error}}};function M(e){if(!e)return{};const{errorMap:t,invalid_type_error:r,required_error:n,description:a}=e;if(t&&(r||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return t?{errorMap:t,description:a}:{errorMap:(o,i)=>{const{message:u}=e;return o.code==="invalid_enum_value"?{message:u??i.defaultError}:typeof i.data>"u"?{message:u??n??i.defaultError}:o.code!=="invalid_type"?{message:i.defaultError}:{message:u??r??i.defaultError}},description:a}}class L{get description(){return this._def.description}_getType(t){return we(t.data)}_getOrReturnCtx(t,r){return r||{common:t.parent.common,data:t.data,parsedType:we(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}_processInputParams(t){return{status:new le,ctx:{common:t.parent.common,data:t.data,parsedType:we(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}}_parseSync(t){const r=this._parse(t);if(zt(r))throw new Error("Synchronous parse encountered promise.");return r}_parseAsync(t){const r=this._parse(t);return Promise.resolve(r)}parse(t,r){const n=this.safeParse(t,r);if(n.success)return n.data;throw n.error}safeParse(t,r){const n={common:{issues:[],async:(r==null?void 0:r.async)??!1,contextualErrorMap:r==null?void 0:r.errorMap},path:(r==null?void 0:r.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:we(t)},a=this._parseSync({data:t,path:n.path,parent:n});return ln(n,a)}"~validate"(t){var n,a;const r={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:we(t)};if(!this["~standard"].async)try{const s=this._parseSync({data:t,path:[],parent:r});return Le(s)?{value:s.value}:{issues:r.common.issues}}catch(s){(a=(n=s==null?void 0:s.message)==null?void 0:n.toLowerCase())!=null&&a.includes("encountered")&&(this["~standard"].async=!0),r.common={issues:[],async:!0}}return this._parseAsync({data:t,path:[],parent:r}).then(s=>Le(s)?{value:s.value}:{issues:r.common.issues})}async parseAsync(t,r){const n=await this.safeParseAsync(t,r);if(n.success)return n.data;throw n.error}async safeParseAsync(t,r){const n={common:{issues:[],contextualErrorMap:r==null?void 0:r.errorMap,async:!0},path:(r==null?void 0:r.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:we(t)},a=this._parse({data:t,path:n.path,parent:n}),s=await(zt(a)?a:Promise.resolve(a));return ln(n,s)}refine(t,r){const n=a=>typeof r=="string"||typeof r>"u"?{message:r}:typeof r=="function"?r(a):r;return this._refinement((a,s)=>{const o=t(a),i=()=>s.addIssue({code:w.custom,...n(a)});return typeof Promise<"u"&&o instanceof Promise?o.then(u=>u?!0:(i(),!1)):o?!0:(i(),!1)})}refinement(t,r){return this._refinement((n,a)=>t(n)?!0:(a.addIssue(typeof r=="function"?r(n,a):r),!1))}_refinement(t){return new Fe({schema:this,typeName:k.ZodEffects,effect:{type:"refinement",refinement:t}})}superRefine(t){return this._refinement(t)}constructor(t){this.spa=this.safeParseAsync,this._def=t,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:r=>this["~validate"](r)}}optional(){return ve.create(this,this._def)}nullable(){return Ve.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return me.create(this)}promise(){return Ot.create(this,this._def)}or(t){return $t.create([this,t],this._def)}and(t){return Zt.create(this,t,this._def)}transform(t){return new Fe({...M(this._def),schema:this,typeName:k.ZodEffects,effect:{type:"transform",transform:t}})}default(t){const r=typeof t=="function"?t:()=>t;return new or({...M(this._def),innerType:this,defaultValue:r,typeName:k.ZodDefault})}brand(){return new xl({typeName:k.ZodBranded,type:this,...M(this._def)})}catch(t){const r=typeof t=="function"?t:()=>t;return new ir({...M(this._def),innerType:this,catchValue:r,typeName:k.ZodCatch})}describe(t){const r=this.constructor;return new r({...this._def,description:t})}pipe(t){return xr.create(this,t)}readonly(){return cr.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const el=/^c[^\s-]{8,}$/i,tl=/^[0-9a-z]+$/,rl=/^[0-9A-HJKMNP-TV-Z]{26}$/i,nl=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,al=/^[a-z0-9_-]{21}$/i,sl=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,ol=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,il=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,cl="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let Kt;const ul=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,ll=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,dl=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,fl=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,pl=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,hl=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Ea="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",ml=new RegExp(`^${Ea}$`);function za(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:e.precision==null&&(t=`${t}(\\.\\d+)?`);const r=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${r}`}function gl(e){return new RegExp(`^${za(e)}$`)}function vl(e){let t=`${Ea}T${za(e)}`;const r=[];return r.push(e.local?"Z?":"Z"),e.offset&&r.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${r.join("|")})`,new RegExp(`^${t}$`)}function yl(e,t){return!!((t==="v4"||!t)&&ul.test(e)||(t==="v6"||!t)&&dl.test(e))}function _l(e,t){if(!sl.test(e))return!1;try{const[r]=e.split(".");if(!r)return!1;const n=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),a=JSON.parse(atob(n));return!(typeof a!="object"||a===null||"typ"in a&&(a==null?void 0:a.typ)!=="JWT"||!a.alg||t&&a.alg!==t)}catch{return!1}}function bl(e,t){return!!((t==="v4"||!t)&&ll.test(e)||(t==="v6"||!t)&&fl.test(e))}class Ze extends L{_parse(t){if(this._def.coerce&&(t.data=String(t.data)),this._getType(t)!==O.string){const s=this._getOrReturnCtx(t);return E(s,{code:w.invalid_type,expected:O.string,received:s.parsedType}),R}const n=new le;let a;for(const s of this._def.checks)if(s.kind==="min")t.data.length<s.value&&(a=this._getOrReturnCtx(t,a),E(a,{code:w.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),n.dirty());else if(s.kind==="max")t.data.length>s.value&&(a=this._getOrReturnCtx(t,a),E(a,{code:w.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),n.dirty());else if(s.kind==="length"){const o=t.data.length>s.value,i=t.data.length<s.value;(o||i)&&(a=this._getOrReturnCtx(t,a),o?E(a,{code:w.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}):i&&E(a,{code:w.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}),n.dirty())}else if(s.kind==="email")il.test(t.data)||(a=this._getOrReturnCtx(t,a),E(a,{validation:"email",code:w.invalid_string,message:s.message}),n.dirty());else if(s.kind==="emoji")Kt||(Kt=new RegExp(cl,"u")),Kt.test(t.data)||(a=this._getOrReturnCtx(t,a),E(a,{validation:"emoji",code:w.invalid_string,message:s.message}),n.dirty());else if(s.kind==="uuid")nl.test(t.data)||(a=this._getOrReturnCtx(t,a),E(a,{validation:"uuid",code:w.invalid_string,message:s.message}),n.dirty());else if(s.kind==="nanoid")al.test(t.data)||(a=this._getOrReturnCtx(t,a),E(a,{validation:"nanoid",code:w.invalid_string,message:s.message}),n.dirty());else if(s.kind==="cuid")el.test(t.data)||(a=this._getOrReturnCtx(t,a),E(a,{validation:"cuid",code:w.invalid_string,message:s.message}),n.dirty());else if(s.kind==="cuid2")tl.test(t.data)||(a=this._getOrReturnCtx(t,a),E(a,{validation:"cuid2",code:w.invalid_string,message:s.message}),n.dirty());else if(s.kind==="ulid")rl.test(t.data)||(a=this._getOrReturnCtx(t,a),E(a,{validation:"ulid",code:w.invalid_string,message:s.message}),n.dirty());else if(s.kind==="url")try{new URL(t.data)}catch{a=this._getOrReturnCtx(t,a),E(a,{validation:"url",code:w.invalid_string,message:s.message}),n.dirty()}else s.kind==="regex"?(s.regex.lastIndex=0,s.regex.test(t.data)||(a=this._getOrReturnCtx(t,a),E(a,{validation:"regex",code:w.invalid_string,message:s.message}),n.dirty())):s.kind==="trim"?t.data=t.data.trim():s.kind==="includes"?t.data.includes(s.value,s.position)||(a=this._getOrReturnCtx(t,a),E(a,{code:w.invalid_string,validation:{includes:s.value,position:s.position},message:s.message}),n.dirty()):s.kind==="toLowerCase"?t.data=t.data.toLowerCase():s.kind==="toUpperCase"?t.data=t.data.toUpperCase():s.kind==="startsWith"?t.data.startsWith(s.value)||(a=this._getOrReturnCtx(t,a),E(a,{code:w.invalid_string,validation:{startsWith:s.value},message:s.message}),n.dirty()):s.kind==="endsWith"?t.data.endsWith(s.value)||(a=this._getOrReturnCtx(t,a),E(a,{code:w.invalid_string,validation:{endsWith:s.value},message:s.message}),n.dirty()):s.kind==="datetime"?vl(s).test(t.data)||(a=this._getOrReturnCtx(t,a),E(a,{code:w.invalid_string,validation:"datetime",message:s.message}),n.dirty()):s.kind==="date"?ml.test(t.data)||(a=this._getOrReturnCtx(t,a),E(a,{code:w.invalid_string,validation:"date",message:s.message}),n.dirty()):s.kind==="time"?gl(s).test(t.data)||(a=this._getOrReturnCtx(t,a),E(a,{code:w.invalid_string,validation:"time",message:s.message}),n.dirty()):s.kind==="duration"?ol.test(t.data)||(a=this._getOrReturnCtx(t,a),E(a,{validation:"duration",code:w.invalid_string,message:s.message}),n.dirty()):s.kind==="ip"?yl(t.data,s.version)||(a=this._getOrReturnCtx(t,a),E(a,{validation:"ip",code:w.invalid_string,message:s.message}),n.dirty()):s.kind==="jwt"?_l(t.data,s.alg)||(a=this._getOrReturnCtx(t,a),E(a,{validation:"jwt",code:w.invalid_string,message:s.message}),n.dirty()):s.kind==="cidr"?bl(t.data,s.version)||(a=this._getOrReturnCtx(t,a),E(a,{validation:"cidr",code:w.invalid_string,message:s.message}),n.dirty()):s.kind==="base64"?pl.test(t.data)||(a=this._getOrReturnCtx(t,a),E(a,{validation:"base64",code:w.invalid_string,message:s.message}),n.dirty()):s.kind==="base64url"?hl.test(t.data)||(a=this._getOrReturnCtx(t,a),E(a,{validation:"base64url",code:w.invalid_string,message:s.message}),n.dirty()):U.assertNever(s);return{status:n.value,value:t.data}}_regex(t,r,n){return this.refinement(a=>t.test(a),{validation:r,code:w.invalid_string,...C.errToObj(n)})}_addCheck(t){return new Ze({...this._def,checks:[...this._def.checks,t]})}email(t){return this._addCheck({kind:"email",...C.errToObj(t)})}url(t){return this._addCheck({kind:"url",...C.errToObj(t)})}emoji(t){return this._addCheck({kind:"emoji",...C.errToObj(t)})}uuid(t){return this._addCheck({kind:"uuid",...C.errToObj(t)})}nanoid(t){return this._addCheck({kind:"nanoid",...C.errToObj(t)})}cuid(t){return this._addCheck({kind:"cuid",...C.errToObj(t)})}cuid2(t){return this._addCheck({kind:"cuid2",...C.errToObj(t)})}ulid(t){return this._addCheck({kind:"ulid",...C.errToObj(t)})}base64(t){return this._addCheck({kind:"base64",...C.errToObj(t)})}base64url(t){return this._addCheck({kind:"base64url",...C.errToObj(t)})}jwt(t){return this._addCheck({kind:"jwt",...C.errToObj(t)})}ip(t){return this._addCheck({kind:"ip",...C.errToObj(t)})}cidr(t){return this._addCheck({kind:"cidr",...C.errToObj(t)})}datetime(t){return typeof t=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:t}):this._addCheck({kind:"datetime",precision:typeof(t==null?void 0:t.precision)>"u"?null:t==null?void 0:t.precision,offset:(t==null?void 0:t.offset)??!1,local:(t==null?void 0:t.local)??!1,...C.errToObj(t==null?void 0:t.message)})}date(t){return this._addCheck({kind:"date",message:t})}time(t){return typeof t=="string"?this._addCheck({kind:"time",precision:null,message:t}):this._addCheck({kind:"time",precision:typeof(t==null?void 0:t.precision)>"u"?null:t==null?void 0:t.precision,...C.errToObj(t==null?void 0:t.message)})}duration(t){return this._addCheck({kind:"duration",...C.errToObj(t)})}regex(t,r){return this._addCheck({kind:"regex",regex:t,...C.errToObj(r)})}includes(t,r){return this._addCheck({kind:"includes",value:t,position:r==null?void 0:r.position,...C.errToObj(r==null?void 0:r.message)})}startsWith(t,r){return this._addCheck({kind:"startsWith",value:t,...C.errToObj(r)})}endsWith(t,r){return this._addCheck({kind:"endsWith",value:t,...C.errToObj(r)})}min(t,r){return this._addCheck({kind:"min",value:t,...C.errToObj(r)})}max(t,r){return this._addCheck({kind:"max",value:t,...C.errToObj(r)})}length(t,r){return this._addCheck({kind:"length",value:t,...C.errToObj(r)})}nonempty(t){return this.min(1,C.errToObj(t))}trim(){return new Ze({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new Ze({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new Ze({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(t=>t.kind==="datetime")}get isDate(){return!!this._def.checks.find(t=>t.kind==="date")}get isTime(){return!!this._def.checks.find(t=>t.kind==="time")}get isDuration(){return!!this._def.checks.find(t=>t.kind==="duration")}get isEmail(){return!!this._def.checks.find(t=>t.kind==="email")}get isURL(){return!!this._def.checks.find(t=>t.kind==="url")}get isEmoji(){return!!this._def.checks.find(t=>t.kind==="emoji")}get isUUID(){return!!this._def.checks.find(t=>t.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(t=>t.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(t=>t.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(t=>t.kind==="cuid2")}get isULID(){return!!this._def.checks.find(t=>t.kind==="ulid")}get isIP(){return!!this._def.checks.find(t=>t.kind==="ip")}get isCIDR(){return!!this._def.checks.find(t=>t.kind==="cidr")}get isBase64(){return!!this._def.checks.find(t=>t.kind==="base64")}get isBase64url(){return!!this._def.checks.find(t=>t.kind==="base64url")}get minLength(){let t=null;for(const r of this._def.checks)r.kind==="min"&&(t===null||r.value>t)&&(t=r.value);return t}get maxLength(){let t=null;for(const r of this._def.checks)r.kind==="max"&&(t===null||r.value<t)&&(t=r.value);return t}}Ze.create=e=>new Ze({checks:[],typeName:k.ZodString,coerce:(e==null?void 0:e.coerce)??!1,...M(e)});function wl(e,t){const r=(e.toString().split(".")[1]||"").length,n=(t.toString().split(".")[1]||"").length,a=r>n?r:n,s=Number.parseInt(e.toFixed(a).replace(".","")),o=Number.parseInt(t.toFixed(a).replace(".",""));return s%o/10**a}class nt extends L{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(t){if(this._def.coerce&&(t.data=Number(t.data)),this._getType(t)!==O.number){const s=this._getOrReturnCtx(t);return E(s,{code:w.invalid_type,expected:O.number,received:s.parsedType}),R}let n;const a=new le;for(const s of this._def.checks)s.kind==="int"?U.isInteger(t.data)||(n=this._getOrReturnCtx(t,n),E(n,{code:w.invalid_type,expected:"integer",received:"float",message:s.message}),a.dirty()):s.kind==="min"?(s.inclusive?t.data<s.value:t.data<=s.value)&&(n=this._getOrReturnCtx(t,n),E(n,{code:w.too_small,minimum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),a.dirty()):s.kind==="max"?(s.inclusive?t.data>s.value:t.data>=s.value)&&(n=this._getOrReturnCtx(t,n),E(n,{code:w.too_big,maximum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),a.dirty()):s.kind==="multipleOf"?wl(t.data,s.value)!==0&&(n=this._getOrReturnCtx(t,n),E(n,{code:w.not_multiple_of,multipleOf:s.value,message:s.message}),a.dirty()):s.kind==="finite"?Number.isFinite(t.data)||(n=this._getOrReturnCtx(t,n),E(n,{code:w.not_finite,message:s.message}),a.dirty()):U.assertNever(s);return{status:a.value,value:t.data}}gte(t,r){return this.setLimit("min",t,!0,C.toString(r))}gt(t,r){return this.setLimit("min",t,!1,C.toString(r))}lte(t,r){return this.setLimit("max",t,!0,C.toString(r))}lt(t,r){return this.setLimit("max",t,!1,C.toString(r))}setLimit(t,r,n,a){return new nt({...this._def,checks:[...this._def.checks,{kind:t,value:r,inclusive:n,message:C.toString(a)}]})}_addCheck(t){return new nt({...this._def,checks:[...this._def.checks,t]})}int(t){return this._addCheck({kind:"int",message:C.toString(t)})}positive(t){return this._addCheck({kind:"min",value:0,inclusive:!1,message:C.toString(t)})}negative(t){return this._addCheck({kind:"max",value:0,inclusive:!1,message:C.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:0,inclusive:!0,message:C.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:0,inclusive:!0,message:C.toString(t)})}multipleOf(t,r){return this._addCheck({kind:"multipleOf",value:t,message:C.toString(r)})}finite(t){return this._addCheck({kind:"finite",message:C.toString(t)})}safe(t){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:C.toString(t)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:C.toString(t)})}get minValue(){let t=null;for(const r of this._def.checks)r.kind==="min"&&(t===null||r.value>t)&&(t=r.value);return t}get maxValue(){let t=null;for(const r of this._def.checks)r.kind==="max"&&(t===null||r.value<t)&&(t=r.value);return t}get isInt(){return!!this._def.checks.find(t=>t.kind==="int"||t.kind==="multipleOf"&&U.isInteger(t.value))}get isFinite(){let t=null,r=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(r===null||n.value>r)&&(r=n.value):n.kind==="max"&&(t===null||n.value<t)&&(t=n.value)}return Number.isFinite(r)&&Number.isFinite(t)}}nt.create=e=>new nt({checks:[],typeName:k.ZodNumber,coerce:(e==null?void 0:e.coerce)||!1,...M(e)});class at extends L{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(t){if(this._def.coerce)try{t.data=BigInt(t.data)}catch{return this._getInvalidInput(t)}if(this._getType(t)!==O.bigint)return this._getInvalidInput(t);let n;const a=new le;for(const s of this._def.checks)s.kind==="min"?(s.inclusive?t.data<s.value:t.data<=s.value)&&(n=this._getOrReturnCtx(t,n),E(n,{code:w.too_small,type:"bigint",minimum:s.value,inclusive:s.inclusive,message:s.message}),a.dirty()):s.kind==="max"?(s.inclusive?t.data>s.value:t.data>=s.value)&&(n=this._getOrReturnCtx(t,n),E(n,{code:w.too_big,type:"bigint",maximum:s.value,inclusive:s.inclusive,message:s.message}),a.dirty()):s.kind==="multipleOf"?t.data%s.value!==BigInt(0)&&(n=this._getOrReturnCtx(t,n),E(n,{code:w.not_multiple_of,multipleOf:s.value,message:s.message}),a.dirty()):U.assertNever(s);return{status:a.value,value:t.data}}_getInvalidInput(t){const r=this._getOrReturnCtx(t);return E(r,{code:w.invalid_type,expected:O.bigint,received:r.parsedType}),R}gte(t,r){return this.setLimit("min",t,!0,C.toString(r))}gt(t,r){return this.setLimit("min",t,!1,C.toString(r))}lte(t,r){return this.setLimit("max",t,!0,C.toString(r))}lt(t,r){return this.setLimit("max",t,!1,C.toString(r))}setLimit(t,r,n,a){return new at({...this._def,checks:[...this._def.checks,{kind:t,value:r,inclusive:n,message:C.toString(a)}]})}_addCheck(t){return new at({...this._def,checks:[...this._def.checks,t]})}positive(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:C.toString(t)})}negative(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:C.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:C.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:C.toString(t)})}multipleOf(t,r){return this._addCheck({kind:"multipleOf",value:t,message:C.toString(r)})}get minValue(){let t=null;for(const r of this._def.checks)r.kind==="min"&&(t===null||r.value>t)&&(t=r.value);return t}get maxValue(){let t=null;for(const r of this._def.checks)r.kind==="max"&&(t===null||r.value<t)&&(t=r.value);return t}}at.create=e=>new at({checks:[],typeName:k.ZodBigInt,coerce:(e==null?void 0:e.coerce)??!1,...M(e)});class dn extends L{_parse(t){if(this._def.coerce&&(t.data=!!t.data),this._getType(t)!==O.boolean){const n=this._getOrReturnCtx(t);return E(n,{code:w.invalid_type,expected:O.boolean,received:n.parsedType}),R}return fe(t.data)}}dn.create=e=>new dn({typeName:k.ZodBoolean,coerce:(e==null?void 0:e.coerce)||!1,...M(e)});class At extends L{_parse(t){if(this._def.coerce&&(t.data=new Date(t.data)),this._getType(t)!==O.date){const s=this._getOrReturnCtx(t);return E(s,{code:w.invalid_type,expected:O.date,received:s.parsedType}),R}if(Number.isNaN(t.data.getTime())){const s=this._getOrReturnCtx(t);return E(s,{code:w.invalid_date}),R}const n=new le;let a;for(const s of this._def.checks)s.kind==="min"?t.data.getTime()<s.value&&(a=this._getOrReturnCtx(t,a),E(a,{code:w.too_small,message:s.message,inclusive:!0,exact:!1,minimum:s.value,type:"date"}),n.dirty()):s.kind==="max"?t.data.getTime()>s.value&&(a=this._getOrReturnCtx(t,a),E(a,{code:w.too_big,message:s.message,inclusive:!0,exact:!1,maximum:s.value,type:"date"}),n.dirty()):U.assertNever(s);return{status:n.value,value:new Date(t.data.getTime())}}_addCheck(t){return new At({...this._def,checks:[...this._def.checks,t]})}min(t,r){return this._addCheck({kind:"min",value:t.getTime(),message:C.toString(r)})}max(t,r){return this._addCheck({kind:"max",value:t.getTime(),message:C.toString(r)})}get minDate(){let t=null;for(const r of this._def.checks)r.kind==="min"&&(t===null||r.value>t)&&(t=r.value);return t!=null?new Date(t):null}get maxDate(){let t=null;for(const r of this._def.checks)r.kind==="max"&&(t===null||r.value<t)&&(t=r.value);return t!=null?new Date(t):null}}At.create=e=>new At({checks:[],coerce:(e==null?void 0:e.coerce)||!1,typeName:k.ZodDate,...M(e)});class fn extends L{_parse(t){if(this._getType(t)!==O.symbol){const n=this._getOrReturnCtx(t);return E(n,{code:w.invalid_type,expected:O.symbol,received:n.parsedType}),R}return fe(t.data)}}fn.create=e=>new fn({typeName:k.ZodSymbol,...M(e)});class pn extends L{_parse(t){if(this._getType(t)!==O.undefined){const n=this._getOrReturnCtx(t);return E(n,{code:w.invalid_type,expected:O.undefined,received:n.parsedType}),R}return fe(t.data)}}pn.create=e=>new pn({typeName:k.ZodUndefined,...M(e)});class hn extends L{_parse(t){if(this._getType(t)!==O.null){const n=this._getOrReturnCtx(t);return E(n,{code:w.invalid_type,expected:O.null,received:n.parsedType}),R}return fe(t.data)}}hn.create=e=>new hn({typeName:k.ZodNull,...M(e)});class mn extends L{constructor(){super(...arguments),this._any=!0}_parse(t){return fe(t.data)}}mn.create=e=>new mn({typeName:k.ZodAny,...M(e)});class gn extends L{constructor(){super(...arguments),this._unknown=!0}_parse(t){return fe(t.data)}}gn.create=e=>new gn({typeName:k.ZodUnknown,...M(e)});class Te extends L{_parse(t){const r=this._getOrReturnCtx(t);return E(r,{code:w.invalid_type,expected:O.never,received:r.parsedType}),R}}Te.create=e=>new Te({typeName:k.ZodNever,...M(e)});class vn extends L{_parse(t){if(this._getType(t)!==O.undefined){const n=this._getOrReturnCtx(t);return E(n,{code:w.invalid_type,expected:O.void,received:n.parsedType}),R}return fe(t.data)}}vn.create=e=>new vn({typeName:k.ZodVoid,...M(e)});class me extends L{_parse(t){const{ctx:r,status:n}=this._processInputParams(t),a=this._def;if(r.parsedType!==O.array)return E(r,{code:w.invalid_type,expected:O.array,received:r.parsedType}),R;if(a.exactLength!==null){const o=r.data.length>a.exactLength.value,i=r.data.length<a.exactLength.value;(o||i)&&(E(r,{code:o?w.too_big:w.too_small,minimum:i?a.exactLength.value:void 0,maximum:o?a.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:a.exactLength.message}),n.dirty())}if(a.minLength!==null&&r.data.length<a.minLength.value&&(E(r,{code:w.too_small,minimum:a.minLength.value,type:"array",inclusive:!0,exact:!1,message:a.minLength.message}),n.dirty()),a.maxLength!==null&&r.data.length>a.maxLength.value&&(E(r,{code:w.too_big,maximum:a.maxLength.value,type:"array",inclusive:!0,exact:!1,message:a.maxLength.message}),n.dirty()),r.common.async)return Promise.all([...r.data].map((o,i)=>a.type._parseAsync(new Se(r,o,r.path,i)))).then(o=>le.mergeArray(n,o));const s=[...r.data].map((o,i)=>a.type._parseSync(new Se(r,o,r.path,i)));return le.mergeArray(n,s)}get element(){return this._def.type}min(t,r){return new me({...this._def,minLength:{value:t,message:C.toString(r)}})}max(t,r){return new me({...this._def,maxLength:{value:t,message:C.toString(r)}})}length(t,r){return new me({...this._def,exactLength:{value:t,message:C.toString(r)}})}nonempty(t){return this.min(1,t)}}me.create=(e,t)=>new me({type:e,minLength:null,maxLength:null,exactLength:null,typeName:k.ZodArray,...M(t)});function je(e){if(e instanceof re){const t={};for(const r in e.shape){const n=e.shape[r];t[r]=ve.create(je(n))}return new re({...e._def,shape:()=>t})}else return e instanceof me?new me({...e._def,type:je(e.element)}):e instanceof ve?ve.create(je(e.unwrap())):e instanceof Ve?Ve.create(je(e.unwrap())):e instanceof Ne?Ne.create(e.items.map(t=>je(t))):e}class re extends L{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const t=this._def.shape(),r=U.objectKeys(t);return this._cached={shape:t,keys:r},this._cached}_parse(t){if(this._getType(t)!==O.object){const c=this._getOrReturnCtx(t);return E(c,{code:w.invalid_type,expected:O.object,received:c.parsedType}),R}const{status:n,ctx:a}=this._processInputParams(t),{shape:s,keys:o}=this._getCached(),i=[];if(!(this._def.catchall instanceof Te&&this._def.unknownKeys==="strip"))for(const c in a.data)o.includes(c)||i.push(c);const u=[];for(const c of o){const d=s[c],g=a.data[c];u.push({key:{status:"valid",value:c},value:d._parse(new Se(a,g,a.path,c)),alwaysSet:c in a.data})}if(this._def.catchall instanceof Te){const c=this._def.unknownKeys;if(c==="passthrough")for(const d of i)u.push({key:{status:"valid",value:d},value:{status:"valid",value:a.data[d]}});else if(c==="strict")i.length>0&&(E(a,{code:w.unrecognized_keys,keys:i}),n.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const d of i){const g=a.data[d];u.push({key:{status:"valid",value:d},value:c._parse(new Se(a,g,a.path,d)),alwaysSet:d in a.data})}}return a.common.async?Promise.resolve().then(async()=>{const c=[];for(const d of u){const g=await d.key,x=await d.value;c.push({key:g,value:x,alwaysSet:d.alwaysSet})}return c}).then(c=>le.mergeObjectSync(n,c)):le.mergeObjectSync(n,u)}get shape(){return this._def.shape()}strict(t){return C.errToObj,new re({...this._def,unknownKeys:"strict",...t!==void 0?{errorMap:(r,n)=>{var s,o;const a=((o=(s=this._def).errorMap)==null?void 0:o.call(s,r,n).message)??n.defaultError;return r.code==="unrecognized_keys"?{message:C.errToObj(t).message??a}:{message:a}}}:{}})}strip(){return new re({...this._def,unknownKeys:"strip"})}passthrough(){return new re({...this._def,unknownKeys:"passthrough"})}extend(t){return new re({...this._def,shape:()=>({...this._def.shape(),...t})})}merge(t){return new re({unknownKeys:t._def.unknownKeys,catchall:t._def.catchall,shape:()=>({...this._def.shape(),...t._def.shape()}),typeName:k.ZodObject})}setKey(t,r){return this.augment({[t]:r})}catchall(t){return new re({...this._def,catchall:t})}pick(t){const r={};for(const n of U.objectKeys(t))t[n]&&this.shape[n]&&(r[n]=this.shape[n]);return new re({...this._def,shape:()=>r})}omit(t){const r={};for(const n of U.objectKeys(this.shape))t[n]||(r[n]=this.shape[n]);return new re({...this._def,shape:()=>r})}deepPartial(){return je(this)}partial(t){const r={};for(const n of U.objectKeys(this.shape)){const a=this.shape[n];t&&!t[n]?r[n]=a:r[n]=a.optional()}return new re({...this._def,shape:()=>r})}required(t){const r={};for(const n of U.objectKeys(this.shape))if(t&&!t[n])r[n]=this.shape[n];else{let s=this.shape[n];for(;s instanceof ve;)s=s._def.innerType;r[n]=s}return new re({...this._def,shape:()=>r})}keyof(){return Aa(U.objectKeys(this.shape))}}re.create=(e,t)=>new re({shape:()=>e,unknownKeys:"strip",catchall:Te.create(),typeName:k.ZodObject,...M(t)});re.strictCreate=(e,t)=>new re({shape:()=>e,unknownKeys:"strict",catchall:Te.create(),typeName:k.ZodObject,...M(t)});re.lazycreate=(e,t)=>new re({shape:e,unknownKeys:"strip",catchall:Te.create(),typeName:k.ZodObject,...M(t)});class $t extends L{_parse(t){const{ctx:r}=this._processInputParams(t),n=this._def.options;function a(s){for(const i of s)if(i.result.status==="valid")return i.result;for(const i of s)if(i.result.status==="dirty")return r.common.issues.push(...i.ctx.common.issues),i.result;const o=s.map(i=>new ye(i.ctx.common.issues));return E(r,{code:w.invalid_union,unionErrors:o}),R}if(r.common.async)return Promise.all(n.map(async s=>{const o={...r,common:{...r.common,issues:[]},parent:null};return{result:await s._parseAsync({data:r.data,path:r.path,parent:o}),ctx:o}})).then(a);{let s;const o=[];for(const u of n){const c={...r,common:{...r.common,issues:[]},parent:null},d=u._parseSync({data:r.data,path:r.path,parent:c});if(d.status==="valid")return d;d.status==="dirty"&&!s&&(s={result:d,ctx:c}),c.common.issues.length&&o.push(c.common.issues)}if(s)return r.common.issues.push(...s.ctx.common.issues),s.result;const i=o.map(u=>new ye(u));return E(r,{code:w.invalid_union,unionErrors:i}),R}}get options(){return this._def.options}}$t.create=(e,t)=>new $t({options:e,typeName:k.ZodUnion,...M(t)});function sr(e,t){const r=we(e),n=we(t);if(e===t)return{valid:!0,data:e};if(r===O.object&&n===O.object){const a=U.objectKeys(t),s=U.objectKeys(e).filter(i=>a.indexOf(i)!==-1),o={...e,...t};for(const i of s){const u=sr(e[i],t[i]);if(!u.valid)return{valid:!1};o[i]=u.data}return{valid:!0,data:o}}else if(r===O.array&&n===O.array){if(e.length!==t.length)return{valid:!1};const a=[];for(let s=0;s<e.length;s++){const o=e[s],i=t[s],u=sr(o,i);if(!u.valid)return{valid:!1};a.push(u.data)}return{valid:!0,data:a}}else return r===O.date&&n===O.date&&+e==+t?{valid:!0,data:e}:{valid:!1}}class Zt extends L{_parse(t){const{status:r,ctx:n}=this._processInputParams(t),a=(s,o)=>{if(cn(s)||cn(o))return R;const i=sr(s.value,o.value);return i.valid?((un(s)||un(o))&&r.dirty(),{status:r.value,value:i.data}):(E(n,{code:w.invalid_intersection_types}),R)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([s,o])=>a(s,o)):a(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}Zt.create=(e,t,r)=>new Zt({left:e,right:t,typeName:k.ZodIntersection,...M(r)});class Ne extends L{_parse(t){const{status:r,ctx:n}=this._processInputParams(t);if(n.parsedType!==O.array)return E(n,{code:w.invalid_type,expected:O.array,received:n.parsedType}),R;if(n.data.length<this._def.items.length)return E(n,{code:w.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),R;!this._def.rest&&n.data.length>this._def.items.length&&(E(n,{code:w.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),r.dirty());const s=[...n.data].map((o,i)=>{const u=this._def.items[i]||this._def.rest;return u?u._parse(new Se(n,o,n.path,i)):null}).filter(o=>!!o);return n.common.async?Promise.all(s).then(o=>le.mergeArray(r,o)):le.mergeArray(r,s)}get items(){return this._def.items}rest(t){return new Ne({...this._def,rest:t})}}Ne.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Ne({items:e,typeName:k.ZodTuple,rest:null,...M(t)})};class yn extends L{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:r,ctx:n}=this._processInputParams(t);if(n.parsedType!==O.map)return E(n,{code:w.invalid_type,expected:O.map,received:n.parsedType}),R;const a=this._def.keyType,s=this._def.valueType,o=[...n.data.entries()].map(([i,u],c)=>({key:a._parse(new Se(n,i,n.path,[c,"key"])),value:s._parse(new Se(n,u,n.path,[c,"value"]))}));if(n.common.async){const i=new Map;return Promise.resolve().then(async()=>{for(const u of o){const c=await u.key,d=await u.value;if(c.status==="aborted"||d.status==="aborted")return R;(c.status==="dirty"||d.status==="dirty")&&r.dirty(),i.set(c.value,d.value)}return{status:r.value,value:i}})}else{const i=new Map;for(const u of o){const c=u.key,d=u.value;if(c.status==="aborted"||d.status==="aborted")return R;(c.status==="dirty"||d.status==="dirty")&&r.dirty(),i.set(c.value,d.value)}return{status:r.value,value:i}}}}yn.create=(e,t,r)=>new yn({valueType:t,keyType:e,typeName:k.ZodMap,...M(r)});class st extends L{_parse(t){const{status:r,ctx:n}=this._processInputParams(t);if(n.parsedType!==O.set)return E(n,{code:w.invalid_type,expected:O.set,received:n.parsedType}),R;const a=this._def;a.minSize!==null&&n.data.size<a.minSize.value&&(E(n,{code:w.too_small,minimum:a.minSize.value,type:"set",inclusive:!0,exact:!1,message:a.minSize.message}),r.dirty()),a.maxSize!==null&&n.data.size>a.maxSize.value&&(E(n,{code:w.too_big,maximum:a.maxSize.value,type:"set",inclusive:!0,exact:!1,message:a.maxSize.message}),r.dirty());const s=this._def.valueType;function o(u){const c=new Set;for(const d of u){if(d.status==="aborted")return R;d.status==="dirty"&&r.dirty(),c.add(d.value)}return{status:r.value,value:c}}const i=[...n.data.values()].map((u,c)=>s._parse(new Se(n,u,n.path,c)));return n.common.async?Promise.all(i).then(u=>o(u)):o(i)}min(t,r){return new st({...this._def,minSize:{value:t,message:C.toString(r)}})}max(t,r){return new st({...this._def,maxSize:{value:t,message:C.toString(r)}})}size(t,r){return this.min(t,r).max(t,r)}nonempty(t){return this.min(1,t)}}st.create=(e,t)=>new st({valueType:e,minSize:null,maxSize:null,typeName:k.ZodSet,...M(t)});class _n extends L{get schema(){return this._def.getter()}_parse(t){const{ctx:r}=this._processInputParams(t);return this._def.getter()._parse({data:r.data,path:r.path,parent:r})}}_n.create=(e,t)=>new _n({getter:e,typeName:k.ZodLazy,...M(t)});class bn extends L{_parse(t){if(t.data!==this._def.value){const r=this._getOrReturnCtx(t);return E(r,{received:r.data,code:w.invalid_literal,expected:this._def.value}),R}return{status:"valid",value:t.data}}get value(){return this._def.value}}bn.create=(e,t)=>new bn({value:e,typeName:k.ZodLiteral,...M(t)});function Aa(e,t){return new Ue({values:e,typeName:k.ZodEnum,...M(t)})}class Ue extends L{_parse(t){if(typeof t.data!="string"){const r=this._getOrReturnCtx(t),n=this._def.values;return E(r,{expected:U.joinValues(n),received:r.parsedType,code:w.invalid_type}),R}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(t.data)){const r=this._getOrReturnCtx(t),n=this._def.values;return E(r,{received:r.data,code:w.invalid_enum_value,options:n}),R}return fe(t.data)}get options(){return this._def.values}get enum(){const t={};for(const r of this._def.values)t[r]=r;return t}get Values(){const t={};for(const r of this._def.values)t[r]=r;return t}get Enum(){const t={};for(const r of this._def.values)t[r]=r;return t}extract(t,r=this._def){return Ue.create(t,{...this._def,...r})}exclude(t,r=this._def){return Ue.create(this.options.filter(n=>!t.includes(n)),{...this._def,...r})}}Ue.create=Aa;class wn extends L{_parse(t){const r=U.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(t);if(n.parsedType!==O.string&&n.parsedType!==O.number){const a=U.objectValues(r);return E(n,{expected:U.joinValues(a),received:n.parsedType,code:w.invalid_type}),R}if(this._cache||(this._cache=new Set(U.getValidEnumValues(this._def.values))),!this._cache.has(t.data)){const a=U.objectValues(r);return E(n,{received:n.data,code:w.invalid_enum_value,options:a}),R}return fe(t.data)}get enum(){return this._def.values}}wn.create=(e,t)=>new wn({values:e,typeName:k.ZodNativeEnum,...M(t)});class Ot extends L{unwrap(){return this._def.type}_parse(t){const{ctx:r}=this._processInputParams(t);if(r.parsedType!==O.promise&&r.common.async===!1)return E(r,{code:w.invalid_type,expected:O.promise,received:r.parsedType}),R;const n=r.parsedType===O.promise?r.data:Promise.resolve(r.data);return fe(n.then(a=>this._def.type.parseAsync(a,{path:r.path,errorMap:r.common.contextualErrorMap})))}}Ot.create=(e,t)=>new Ot({type:e,typeName:k.ZodPromise,...M(t)});class Fe extends L{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===k.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(t){const{status:r,ctx:n}=this._processInputParams(t),a=this._def.effect||null,s={addIssue:o=>{E(n,o),o.fatal?r.abort():r.dirty()},get path(){return n.path}};if(s.addIssue=s.addIssue.bind(s),a.type==="preprocess"){const o=a.transform(n.data,s);if(n.common.async)return Promise.resolve(o).then(async i=>{if(r.value==="aborted")return R;const u=await this._def.schema._parseAsync({data:i,path:n.path,parent:n});return u.status==="aborted"?R:u.status==="dirty"||r.value==="dirty"?Ke(u.value):u});{if(r.value==="aborted")return R;const i=this._def.schema._parseSync({data:o,path:n.path,parent:n});return i.status==="aborted"?R:i.status==="dirty"||r.value==="dirty"?Ke(i.value):i}}if(a.type==="refinement"){const o=i=>{const u=a.refinement(i,s);if(n.common.async)return Promise.resolve(u);if(u instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return i};if(n.common.async===!1){const i=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return i.status==="aborted"?R:(i.status==="dirty"&&r.dirty(),o(i.value),{status:r.value,value:i.value})}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(i=>i.status==="aborted"?R:(i.status==="dirty"&&r.dirty(),o(i.value).then(()=>({status:r.value,value:i.value}))))}if(a.type==="transform")if(n.common.async===!1){const o=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!Le(o))return R;const i=a.transform(o.value,s);if(i instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:r.value,value:i}}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(o=>Le(o)?Promise.resolve(a.transform(o.value,s)).then(i=>({status:r.value,value:i})):R);U.assertNever(a)}}Fe.create=(e,t,r)=>new Fe({schema:e,typeName:k.ZodEffects,effect:t,...M(r)});Fe.createWithPreprocess=(e,t,r)=>new Fe({schema:t,effect:{type:"preprocess",transform:e},typeName:k.ZodEffects,...M(r)});class ve extends L{_parse(t){return this._getType(t)===O.undefined?fe(void 0):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}ve.create=(e,t)=>new ve({innerType:e,typeName:k.ZodOptional,...M(t)});class Ve extends L{_parse(t){return this._getType(t)===O.null?fe(null):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}Ve.create=(e,t)=>new Ve({innerType:e,typeName:k.ZodNullable,...M(t)});class or extends L{_parse(t){const{ctx:r}=this._processInputParams(t);let n=r.data;return r.parsedType===O.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:r.path,parent:r})}removeDefault(){return this._def.innerType}}or.create=(e,t)=>new or({innerType:e,typeName:k.ZodDefault,defaultValue:typeof t.default=="function"?t.default:()=>t.default,...M(t)});class ir extends L{_parse(t){const{ctx:r}=this._processInputParams(t),n={...r,common:{...r.common,issues:[]}},a=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return zt(a)?a.then(s=>({status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new ye(n.common.issues)},input:n.data})})):{status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new ye(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}ir.create=(e,t)=>new ir({innerType:e,typeName:k.ZodCatch,catchValue:typeof t.catch=="function"?t.catch:()=>t.catch,...M(t)});class xn extends L{_parse(t){if(this._getType(t)!==O.nan){const n=this._getOrReturnCtx(t);return E(n,{code:w.invalid_type,expected:O.nan,received:n.parsedType}),R}return{status:"valid",value:t.data}}}xn.create=e=>new xn({typeName:k.ZodNaN,...M(e)});class xl extends L{_parse(t){const{ctx:r}=this._processInputParams(t),n=r.data;return this._def.type._parse({data:n,path:r.path,parent:r})}unwrap(){return this._def.type}}class xr extends L{_parse(t){const{status:r,ctx:n}=this._processInputParams(t);if(n.common.async)return(async()=>{const s=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return s.status==="aborted"?R:s.status==="dirty"?(r.dirty(),Ke(s.value)):this._def.out._parseAsync({data:s.value,path:n.path,parent:n})})();{const a=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return a.status==="aborted"?R:a.status==="dirty"?(r.dirty(),{status:"dirty",value:a.value}):this._def.out._parseSync({data:a.value,path:n.path,parent:n})}}static create(t,r){return new xr({in:t,out:r,typeName:k.ZodPipeline})}}class cr extends L{_parse(t){const r=this._def.innerType._parse(t),n=a=>(Le(a)&&(a.value=Object.freeze(a.value)),a);return zt(r)?r.then(a=>n(a)):n(r)}unwrap(){return this._def.innerType}}cr.create=(e,t)=>new cr({innerType:e,typeName:k.ZodReadonly,...M(t)});var k;(function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"})(k||(k={}));Te.create;me.create;$t.create;Zt.create;Ne.create;Ue.create;Ot.create;ve.create;Ve.create;function kl(){return{}}function Il(e,t){var n,a,s;const r={type:"array"};return(n=e.type)!=null&&n._def&&((s=(a=e.type)==null?void 0:a._def)==null?void 0:s.typeName)!==k.ZodAny&&(r.items=F(e.type._def,{...t,currentPath:[...t.currentPath,"items"]})),e.minLength&&B(r,"minItems",e.minLength.value,e.minLength.message,t),e.maxLength&&B(r,"maxItems",e.maxLength.value,e.maxLength.message,t),e.exactLength&&(B(r,"minItems",e.exactLength.value,e.exactLength.message,t),B(r,"maxItems",e.exactLength.value,e.exactLength.message,t)),r}function Sl(e,t){const r={type:"integer",format:"int64"};if(!e.checks)return r;for(const n of e.checks)switch(n.kind){case"min":t.target==="jsonSchema7"?n.inclusive?B(r,"minimum",n.value,n.message,t):B(r,"exclusiveMinimum",n.value,n.message,t):(n.inclusive||(r.exclusiveMinimum=!0),B(r,"minimum",n.value,n.message,t));break;case"max":t.target==="jsonSchema7"?n.inclusive?B(r,"maximum",n.value,n.message,t):B(r,"exclusiveMaximum",n.value,n.message,t):(n.inclusive||(r.exclusiveMaximum=!0),B(r,"maximum",n.value,n.message,t));break;case"multipleOf":B(r,"multipleOf",n.value,n.message,t);break}return r}function Tl(){return{type:"boolean"}}function $a(e,t){return F(e.type._def,t)}const El=(e,t)=>F(e.innerType._def,t);function Za(e,t,r){const n=r??t.dateStrategy;if(Array.isArray(n))return{anyOf:n.map((a,s)=>Za(e,t,a))};switch(n){case"string":case"format:date-time":return{type:"string",format:"date-time"};case"format:date":return{type:"string",format:"date"};case"integer":return zl(e,t)}}const zl=(e,t)=>{const r={type:"integer",format:"unix-time"};if(t.target==="openApi3")return r;for(const n of e.checks)switch(n.kind){case"min":B(r,"minimum",n.value,n.message,t);break;case"max":B(r,"maximum",n.value,n.message,t);break}return r};function Al(e,t){return{...F(e.innerType._def,t),default:e.defaultValue()}}function $l(e,t){return t.effectStrategy==="input"?F(e.schema._def,t):{}}function Zl(e){return{type:"string",enum:Array.from(e.values)}}const Ol=e=>"type"in e&&e.type==="string"?!1:"allOf"in e;function Nl(e,t){const r=[F(e.left._def,{...t,currentPath:[...t.currentPath,"allOf","0"]}),F(e.right._def,{...t,currentPath:[...t.currentPath,"allOf","1"]})].filter(s=>!!s);let n=t.target==="jsonSchema2019-09"?{unevaluatedProperties:!1}:void 0;const a=[];return r.forEach(s=>{if(Ol(s))a.push(...s.allOf),s.unevaluatedProperties===void 0&&(n=void 0);else{let o=s;if("additionalProperties"in s&&s.additionalProperties===!1){const{additionalProperties:i,...u}=s;o=u}else n=void 0;a.push(o)}}),a.length?{allOf:a,...n}:void 0}function Cl(e,t){const r=typeof e.value;return r!=="bigint"&&r!=="number"&&r!=="boolean"&&r!=="string"?{type:Array.isArray(e.value)?"array":"object"}:t.target==="openApi3"?{type:r==="bigint"?"integer":r,enum:[e.value]}:{type:r==="bigint"?"integer":r,const:e.value}}let Ht;const pe={cuid:/^[cC][^\s-]{8,}$/,cuid2:/^[0-9a-z]+$/,ulid:/^[0-9A-HJKMNP-TV-Z]{26}$/,email:/^(?!\.)(?!.*\.\.)([a-zA-Z0-9_'+\-\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\-]*\.)+[a-zA-Z]{2,}$/,emoji:()=>(Ht===void 0&&(Ht=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),Ht),uuid:/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/,ipv4:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,ipv4Cidr:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ipv6:/^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/,ipv6Cidr:/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,base64:/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,base64url:/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,nanoid:/^[a-zA-Z0-9_-]{21}$/,jwt:/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/};function Oa(e,t){const r={type:"string"};if(e.checks)for(const n of e.checks)switch(n.kind){case"min":B(r,"minLength",typeof r.minLength=="number"?Math.max(r.minLength,n.value):n.value,n.message,t);break;case"max":B(r,"maxLength",typeof r.maxLength=="number"?Math.min(r.maxLength,n.value):n.value,n.message,t);break;case"email":switch(t.emailStrategy){case"format:email":he(r,"email",n.message,t);break;case"format:idn-email":he(r,"idn-email",n.message,t);break;case"pattern:zod":oe(r,pe.email,n.message,t);break}break;case"url":he(r,"uri",n.message,t);break;case"uuid":he(r,"uuid",n.message,t);break;case"regex":oe(r,n.regex,n.message,t);break;case"cuid":oe(r,pe.cuid,n.message,t);break;case"cuid2":oe(r,pe.cuid2,n.message,t);break;case"startsWith":oe(r,RegExp(`^${Xt(n.value,t)}`),n.message,t);break;case"endsWith":oe(r,RegExp(`${Xt(n.value,t)}$`),n.message,t);break;case"datetime":he(r,"date-time",n.message,t);break;case"date":he(r,"date",n.message,t);break;case"time":he(r,"time",n.message,t);break;case"duration":he(r,"duration",n.message,t);break;case"length":B(r,"minLength",typeof r.minLength=="number"?Math.max(r.minLength,n.value):n.value,n.message,t),B(r,"maxLength",typeof r.maxLength=="number"?Math.min(r.maxLength,n.value):n.value,n.message,t);break;case"includes":{oe(r,RegExp(Xt(n.value,t)),n.message,t);break}case"ip":{n.version!=="v6"&&he(r,"ipv4",n.message,t),n.version!=="v4"&&he(r,"ipv6",n.message,t);break}case"base64url":oe(r,pe.base64url,n.message,t);break;case"jwt":oe(r,pe.jwt,n.message,t);break;case"cidr":{n.version!=="v6"&&oe(r,pe.ipv4Cidr,n.message,t),n.version!=="v4"&&oe(r,pe.ipv6Cidr,n.message,t);break}case"emoji":oe(r,pe.emoji(),n.message,t);break;case"ulid":{oe(r,pe.ulid,n.message,t);break}case"base64":{switch(t.base64Strategy){case"format:binary":{he(r,"binary",n.message,t);break}case"contentEncoding:base64":{B(r,"contentEncoding","base64",n.message,t);break}case"pattern:zod":{oe(r,pe.base64,n.message,t);break}}break}case"nanoid":oe(r,pe.nanoid,n.message,t)}return r}function Xt(e,t){return t.patternStrategy==="escape"?Rl(e):e}const Pl=new Set("ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789");function Rl(e){let t="";for(let r=0;r<e.length;r++)Pl.has(e[r])||(t+="\\"),t+=e[r];return t}function he(e,t,r,n){var a;e.format||(a=e.anyOf)!=null&&a.some(s=>s.format)?(e.anyOf||(e.anyOf=[]),e.format&&(e.anyOf.push({format:e.format,...e.errorMessage&&n.errorMessages&&{errorMessage:{format:e.errorMessage.format}}}),delete e.format,e.errorMessage&&(delete e.errorMessage.format,Object.keys(e.errorMessage).length===0&&delete e.errorMessage)),e.anyOf.push({format:t,...r&&n.errorMessages&&{errorMessage:{format:r}}})):B(e,"format",t,r,n)}function oe(e,t,r,n){var a;e.pattern||(a=e.allOf)!=null&&a.some(s=>s.pattern)?(e.allOf||(e.allOf=[]),e.pattern&&(e.allOf.push({pattern:e.pattern,...e.errorMessage&&n.errorMessages&&{errorMessage:{pattern:e.errorMessage.pattern}}}),delete e.pattern,e.errorMessage&&(delete e.errorMessage.pattern,Object.keys(e.errorMessage).length===0&&delete e.errorMessage)),e.allOf.push({pattern:kn(t,n),...r&&n.errorMessages&&{errorMessage:{pattern:r}}})):B(e,"pattern",kn(t,n),r,n)}function kn(e,t){var u;if(!t.applyRegexFlags||!e.flags)return e.source;const r={i:e.flags.includes("i"),m:e.flags.includes("m"),s:e.flags.includes("s")},n=r.i?e.source.toLowerCase():e.source;let a="",s=!1,o=!1,i=!1;for(let c=0;c<n.length;c++){if(s){a+=n[c],s=!1;continue}if(r.i){if(o){if(n[c].match(/[a-z]/)){i?(a+=n[c],a+=`${n[c-2]}-${n[c]}`.toUpperCase(),i=!1):n[c+1]==="-"&&((u=n[c+2])!=null&&u.match(/[a-z]/))?(a+=n[c],i=!0):a+=`${n[c]}${n[c].toUpperCase()}`;continue}}else if(n[c].match(/[a-z]/)){a+=`[${n[c]}${n[c].toUpperCase()}]`;continue}}if(r.m){if(n[c]==="^"){a+=`(^|(?<=[\r
]))`;continue}else if(n[c]==="$"){a+=`($|(?=[\r
]))`;continue}}if(r.s&&n[c]==="."){a+=o?`${n[c]}\r
`:`[${n[c]}\r
]`;continue}a+=n[c],n[c]==="\\"?s=!0:o&&n[c]==="]"?o=!1:!o&&n[c]==="["&&(o=!0)}try{new RegExp(a)}catch{return console.warn(`Could not convert regex pattern at ${t.currentPath.join("/")} to a flag-independent form! Falling back to the flag-ignorant source`),e.source}return a}function Na(e,t){var n,a,s,o,i,u;if(t.target==="openAi"&&console.warn("Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead."),t.target==="openApi3"&&((n=e.keyType)==null?void 0:n._def.typeName)===k.ZodEnum)return{type:"object",required:e.keyType._def.values,properties:e.keyType._def.values.reduce((c,d)=>({...c,[d]:F(e.valueType._def,{...t,currentPath:[...t.currentPath,"properties",d]})??{}}),{}),additionalProperties:!1};const r={type:"object",additionalProperties:F(e.valueType._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]})??{}};if(t.target==="openApi3")return r;if(((a=e.keyType)==null?void 0:a._def.typeName)===k.ZodString&&((s=e.keyType._def.checks)!=null&&s.length)){const{type:c,...d}=Oa(e.keyType._def,t);return{...r,propertyNames:d}}else{if(((o=e.keyType)==null?void 0:o._def.typeName)===k.ZodEnum)return{...r,propertyNames:{enum:e.keyType._def.values}};if(((i=e.keyType)==null?void 0:i._def.typeName)===k.ZodBranded&&e.keyType._def.type._def.typeName===k.ZodString&&((u=e.keyType._def.type._def.checks)!=null&&u.length)){const{type:c,...d}=$a(e.keyType._def,t);return{...r,propertyNames:d}}}return r}function Ml(e,t){if(t.mapStrategy==="record")return Na(e,t);const r=F(e.keyType._def,{...t,currentPath:[...t.currentPath,"items","items","0"]})||{},n=F(e.valueType._def,{...t,currentPath:[...t.currentPath,"items","items","1"]})||{};return{type:"array",maxItems:125,items:{type:"array",items:[r,n],minItems:2,maxItems:2}}}function jl(e){const t=e.values,n=Object.keys(e.values).filter(s=>typeof t[t[s]]!="number").map(s=>t[s]),a=Array.from(new Set(n.map(s=>typeof s)));return{type:a.length===1?a[0]==="string"?"string":"number":["string","number"],enum:n}}function Dl(){return{not:{}}}function Ll(e){return e.target==="openApi3"?{enum:["null"],nullable:!0}:{type:"null"}}const Nt={ZodString:"string",ZodNumber:"number",ZodBigInt:"integer",ZodBoolean:"boolean",ZodNull:"null"};function Ul(e,t){if(t.target==="openApi3")return In(e,t);const r=e.options instanceof Map?Array.from(e.options.values()):e.options;if(r.every(n=>n._def.typeName in Nt&&(!n._def.checks||!n._def.checks.length))){const n=r.reduce((a,s)=>{const o=Nt[s._def.typeName];return o&&!a.includes(o)?[...a,o]:a},[]);return{type:n.length>1?n:n[0]}}else if(r.every(n=>n._def.typeName==="ZodLiteral"&&!n.description)){const n=r.reduce((a,s)=>{const o=typeof s._def.value;switch(o){case"string":case"number":case"boolean":return[...a,o];case"bigint":return[...a,"integer"];case"object":if(s._def.value===null)return[...a,"null"];case"symbol":case"undefined":case"function":default:return a}},[]);if(n.length===r.length){const a=n.filter((s,o,i)=>i.indexOf(s)===o);return{type:a.length>1?a:a[0],enum:r.reduce((s,o)=>s.includes(o._def.value)?s:[...s,o._def.value],[])}}}else if(r.every(n=>n._def.typeName==="ZodEnum"))return{type:"string",enum:r.reduce((n,a)=>[...n,...a._def.values.filter(s=>!n.includes(s))],[])};return In(e,t)}const In=(e,t)=>{const r=(e.options instanceof Map?Array.from(e.options.values()):e.options).map((n,a)=>F(n._def,{...t,currentPath:[...t.currentPath,"anyOf",`${a}`]})).filter(n=>!!n&&(!t.strictUnions||typeof n=="object"&&Object.keys(n).length>0));return r.length?{anyOf:r}:void 0};function Fl(e,t){if(["ZodString","ZodNumber","ZodBigInt","ZodBoolean","ZodNull"].includes(e.innerType._def.typeName)&&(!e.innerType._def.checks||!e.innerType._def.checks.length))return t.target==="openApi3"?{type:Nt[e.innerType._def.typeName],nullable:!0}:{type:[Nt[e.innerType._def.typeName],"null"]};if(t.target==="openApi3"){const n=F(e.innerType._def,{...t,currentPath:[...t.currentPath]});return n&&"$ref"in n?{allOf:[n],nullable:!0}:n&&{...n,nullable:!0}}const r=F(e.innerType._def,{...t,currentPath:[...t.currentPath,"anyOf","0"]});return r&&{anyOf:[r,{type:"null"}]}}function Vl(e,t){const r={type:"number"};if(!e.checks)return r;for(const n of e.checks)switch(n.kind){case"int":r.type="integer",Ta(r,"type",n.message,t);break;case"min":t.target==="jsonSchema7"?n.inclusive?B(r,"minimum",n.value,n.message,t):B(r,"exclusiveMinimum",n.value,n.message,t):(n.inclusive||(r.exclusiveMinimum=!0),B(r,"minimum",n.value,n.message,t));break;case"max":t.target==="jsonSchema7"?n.inclusive?B(r,"maximum",n.value,n.message,t):B(r,"exclusiveMaximum",n.value,n.message,t):(n.inclusive||(r.exclusiveMaximum=!0),B(r,"maximum",n.value,n.message,t));break;case"multipleOf":B(r,"multipleOf",n.value,n.message,t);break}return r}function Bl(e,t){return t.removeAdditionalStrategy==="strict"?e.catchall._def.typeName==="ZodNever"?e.unknownKeys!=="strict":F(e.catchall._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]})??!0:e.catchall._def.typeName==="ZodNever"?e.unknownKeys==="passthrough":F(e.catchall._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]})??!0}function Jl(e,t){const r=t.target==="openAi",n={type:"object",...Object.entries(e.shape()).reduce((a,[s,o])=>{if(o===void 0||o._def===void 0)return a;let i=o.isOptional();i&&r&&(o instanceof ve&&(o=o._def.innerType),o.isNullable()||(o=o.nullable()),i=!1);const u=F(o._def,{...t,currentPath:[...t.currentPath,"properties",s],propertyPath:[...t.currentPath,"properties",s]});return u===void 0?a:{properties:{...a.properties,[s]:u},required:i?a.required:[...a.required,s]}},{properties:{},required:[]}),additionalProperties:Bl(e,t)};return n.required.length||delete n.required,n}const Gl=(e,t)=>{var n;if(t.currentPath.toString()===((n=t.propertyPath)==null?void 0:n.toString()))return F(e.innerType._def,t);const r=F(e.innerType._def,{...t,currentPath:[...t.currentPath,"anyOf","1"]});return r?{anyOf:[{not:{}},r]}:{}},Wl=(e,t)=>{if(t.pipeStrategy==="input")return F(e.in._def,t);if(t.pipeStrategy==="output")return F(e.out._def,t);const r=F(e.in._def,{...t,currentPath:[...t.currentPath,"allOf","0"]}),n=F(e.out._def,{...t,currentPath:[...t.currentPath,"allOf",r?"1":"0"]});return{allOf:[r,n].filter(a=>a!==void 0)}};function ql(e,t){return F(e.type._def,t)}function Yl(e,t){const n={type:"array",uniqueItems:!0,items:F(e.valueType._def,{...t,currentPath:[...t.currentPath,"items"]})};return e.minSize&&B(n,"minItems",e.minSize.value,e.minSize.message,t),e.maxSize&&B(n,"maxItems",e.maxSize.value,e.maxSize.message,t),n}function Kl(e,t){return e.rest?{type:"array",minItems:e.items.length,items:e.items.map((r,n)=>F(r._def,{...t,currentPath:[...t.currentPath,"items",`${n}`]})).reduce((r,n)=>n===void 0?r:[...r,n],[]),additionalItems:F(e.rest._def,{...t,currentPath:[...t.currentPath,"additionalItems"]})}:{type:"array",minItems:e.items.length,maxItems:e.items.length,items:e.items.map((r,n)=>F(r._def,{...t,currentPath:[...t.currentPath,"items",`${n}`]})).reduce((r,n)=>n===void 0?r:[...r,n],[])}}function Hl(){return{not:{}}}function Xl(){return{}}const Ql=(e,t)=>F(e.innerType._def,t);function F(e,t,r=!1){var o;const n=t.seen.get(e);if(t.override){const i=(o=t.override)==null?void 0:o.call(t,e,t,n,r);if(i!==qu)return i}if(n&&!r){const i=ed(n,t);if(i!==void 0)return i}const a={def:e,path:t.currentPath,jsonSchema:void 0};t.seen.set(e,a);const s=rd(e,e.typeName,t);return s&&nd(e,t,s),a.jsonSchema=s,s}const ed=(e,t)=>{switch(t.$refStrategy){case"root":return{$ref:e.path.join("/")};case"relative":return{$ref:td(t.currentPath,e.path)};case"none":case"seen":return e.path.length<t.currentPath.length&&e.path.every((r,n)=>t.currentPath[n]===r)?(console.warn(`Recursive reference detected at ${t.currentPath.join("/")}! Defaulting to any`),{}):t.$refStrategy==="seen"?{}:void 0}},td=(e,t)=>{let r=0;for(;r<e.length&&r<t.length&&e[r]===t[r];r++);return[(e.length-r).toString(),...t.slice(r)].join("/")},rd=(e,t,r)=>{switch(t){case k.ZodString:return Oa(e,r);case k.ZodNumber:return Vl(e,r);case k.ZodObject:return Jl(e,r);case k.ZodBigInt:return Sl(e,r);case k.ZodBoolean:return Tl();case k.ZodDate:return Za(e,r);case k.ZodUndefined:return Hl();case k.ZodNull:return Ll(r);case k.ZodArray:return Il(e,r);case k.ZodUnion:case k.ZodDiscriminatedUnion:return Ul(e,r);case k.ZodIntersection:return Nl(e,r);case k.ZodTuple:return Kl(e,r);case k.ZodRecord:return Na(e,r);case k.ZodLiteral:return Cl(e,r);case k.ZodEnum:return Zl(e);case k.ZodNativeEnum:return jl(e);case k.ZodNullable:return Fl(e,r);case k.ZodOptional:return Gl(e,r);case k.ZodMap:return Ml(e,r);case k.ZodSet:return Yl(e,r);case k.ZodLazy:return F(e.getter()._def,r);case k.ZodPromise:return ql(e,r);case k.ZodNaN:case k.ZodNever:return Dl();case k.ZodEffects:return $l(e,r);case k.ZodAny:return kl();case k.ZodUnknown:return Xl();case k.ZodDefault:return Al(e,r);case k.ZodBranded:return $a(e,r);case k.ZodReadonly:return Ql(e,r);case k.ZodCatch:return El(e,r);case k.ZodPipeline:return Wl(e,r);case k.ZodFunction:case k.ZodVoid:case k.ZodSymbol:return;default:return(n=>{})()}},nd=(e,t,r)=>(e.description&&(r.description=e.description,t.markdownDescription&&(r.markdownDescription=e.description)),r),ad=(e,t)=>{const r=Ku(t),n=typeof t=="object"&&t.definitions?Object.entries(t.definitions).reduce((u,[c,d])=>({...u,[c]:F(d._def,{...r,currentPath:[...r.basePath,r.definitionPath,c]},!0)??{}}),{}):void 0,a=typeof t=="string"?t:(t==null?void 0:t.nameStrategy)==="title"||t==null?void 0:t.name,s=F(e._def,a===void 0?r:{...r,currentPath:[...r.basePath,r.definitionPath,a]},!1)??{},o=typeof t=="object"&&t.name!==void 0&&t.nameStrategy==="title"?t.name:void 0;o!==void 0&&(s.title=o);const i=a===void 0?n?{...s,[r.definitionPath]:n}:s:{$ref:[...r.$refStrategy==="relative"?[]:r.basePath,r.definitionPath,a].join("/"),[r.definitionPath]:{...n,[a]:s}};return r.target==="jsonSchema7"?i.$schema="http://json-schema.org/draft-07/schema#":(r.target==="jsonSchema2019-09"||r.target==="openAi")&&(i.$schema="https://json-schema.org/draft/2019-09/schema#"),r.target==="openAi"&&("anyOf"in i||"oneOf"in i||"allOf"in i||"type"in i&&Array.isArray(i.type))&&console.warn("Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property."),i};var dt=({prefix:e,size:t=16,alphabet:r="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",separator:n="-"}={})=>{const a=()=>{const s=r.length,o=new Array(t);for(let i=0;i<t;i++)o[i]=r[Math.random()*s|0];return o.join("")};if(e==null)return a;if(r.includes(n))throw new Us({argument:"separator",message:`The separator "${n}" must not be part of the alphabet "${r}".`});return()=>`${e}${n}${a()}`},sd=dt();function ip(e){return(e instanceof Error||e instanceof DOMException)&&(e.name==="AbortError"||e.name==="ResponseAborted"||e.name==="TimeoutError")}var od=/"__proto__"\s*:/,id=/"constructor"\s*:/;function cd(e){const t=JSON.parse(e);return t===null||typeof t!="object"||od.test(e)===!1&&id.test(e)===!1?t:ud(t)}function ud(e){let t=[e];for(;t.length;){const r=t;t=[];for(const n of r){if(Object.prototype.hasOwnProperty.call(n,"__proto__"))throw new SyntaxError("Object contains forbidden prototype property");if(Object.prototype.hasOwnProperty.call(n,"constructor")&&Object.prototype.hasOwnProperty.call(n.constructor,"prototype"))throw new SyntaxError("Object contains forbidden prototype property");for(const a in n){const s=n[a];s&&typeof s=="object"&&t.push(s)}}}return e}function ld(e){const{stackTraceLimit:t}=Error;Error.stackTraceLimit=0;try{return cd(e)}finally{Error.stackTraceLimit=t}}var Ct=Symbol.for("vercel.ai.validator");function dd(e){return{[Ct]:!0,validate:e}}function fd(e){return typeof e=="object"&&e!==null&&Ct in e&&e[Ct]===!0&&"validate"in e}function pd(e){return fd(e)?e:hd(e)}function hd(e){return dd(async t=>{const r=await e["~standard"].validate(t);return r.issues==null?{success:!0,value:r.value}:{success:!1,error:new kt({value:t,cause:r.issues})}})}async function Sn({value:e,schema:t}){const r=await kr({value:e,schema:t});if(!r.success)throw kt.wrap({value:e,cause:r.error});return r.value}async function kr({value:e,schema:t}){const r=pd(t);try{if(r.validate==null)return{success:!0,value:e,rawValue:e};const n=await r.validate(e);return n.success?{success:!0,value:n.value,rawValue:e}:{success:!1,error:kt.wrap({value:e,cause:n.error}),rawValue:e}}catch(n){return{success:!1,error:kt.wrap({value:e,cause:n}),rawValue:e}}}async function Pt({text:e,schema:t}){try{const r=ld(e);return t==null?{success:!0,value:r,rawValue:r}:await kr({value:r,schema:t})}catch(r){return{success:!1,error:jr.isInstance(r)?r:new jr({text:e,cause:r}),rawValue:void 0}}}function Ca({stream:e,schema:t}){return e.pipeThrough(new TextDecoderStream).pipeThrough(new Ws).pipeThrough(new TransformStream({async transform({data:r},n){r!=="[DONE]"&&n.enqueue(await Pt({text:r,schema:t}))}}))}async function Re(e){return typeof e=="function"&&(e=e()),Promise.resolve(e)}function md(e,t){var r;const n=(r=void 0)!=null?r:!1;return Ir(ad(e,{$refStrategy:n?"root":"none",target:"jsonSchema7"}),{validate:async a=>{const s=await e.safeParseAsync(a);return s.success?{success:!0,value:s.data}:{success:!1,error:s.error}}})}function gd(e,t){var r;const n=(r=void 0)!=null?r:!1,a=Lc(e,{target:"draft-7",io:"output",reused:n?"ref":"inline"});return Ir(a,{validate:async s=>{const o=await ya(e,s);return o.success?{success:!0,value:o.data}:{success:!1,error:o.error}}})}function vd(e){return"_zod"in e}function yd(e,t){return vd(e)?gd(e):md(e)}var ur=Symbol.for("vercel.ai.schema");function Ir(e,{validate:t}={}){return{[ur]:!0,_type:void 0,[Ct]:!0,jsonSchema:e,validate:t}}function _d(e){return typeof e=="object"&&e!==null&&ur in e&&e[ur]===!0&&"jsonSchema"in e&&"validate"in e}function bd(e){return e==null?Ir({properties:{},additionalProperties:!1}):_d(e)?e:yd(e)}var wd=Object.defineProperty,xd=(e,t)=>{for(var r in t)wd(e,r,{get:t[r],enumerable:!0})},Pa="AI_NoObjectGeneratedError",Ra=`vercel.ai.error.${Pa}`,kd=Symbol.for(Ra),Ma,Tn=class extends xe{constructor({message:e="No object generated.",cause:t,text:r,response:n,usage:a,finishReason:s}){super({name:Pa,message:e,cause:t}),this[Ma]=!0,this.text=r,this.response=n,this.usage=a,this.finishReason=s}static isInstance(e){return xe.hasMarker(e,Ra)}};Ma=kd;var ja=ne([y(),Et(Uint8Array),Et(ArrayBuffer),Ju(e=>{var t,r;return(r=(t=globalThis.Buffer)==null?void 0:t.isBuffer(e))!=null?r:!1},{message:"Must be a Buffer"})]),ot=Vu(()=>ne([_u(),y(),rt(),Q(),rr(y(),ot),_e(ot)])),D=rr(y(),rr(y(),ot)),Da=P({type:z("text"),text:y(),providerOptions:D.optional()}),Id=P({type:z("image"),image:ne([ja,Et(URL)]),mediaType:y().optional(),providerOptions:D.optional()}),La=P({type:z("file"),data:ne([ja,Et(URL)]),filename:y().optional(),mediaType:y(),providerOptions:D.optional()}),Sd=P({type:z("reasoning"),text:y(),providerOptions:D.optional()}),Td=P({type:z("tool-call"),toolCallId:y(),toolName:y(),input:Y(),providerOptions:D.optional(),providerExecuted:Q().optional()}),Ed=Iu("type",[P({type:z("text"),value:y()}),P({type:z("json"),value:ot}),P({type:z("error-text"),value:y()}),P({type:z("error-json"),value:ot}),P({type:z("content"),value:_e(ne([P({type:z("text"),text:y()}),P({type:z("media"),data:y(),mediaType:y()})]))})]),Ua=P({type:z("tool-result"),toolCallId:y(),toolName:y(),output:Ed,providerOptions:D.optional()}),zd=P({role:z("system"),content:y(),providerOptions:D.optional()}),Ad=P({role:z("user"),content:ne([y(),_e(ne([Da,Id,La]))]),providerOptions:D.optional()}),$d=P({role:z("assistant"),content:ne([y(),_e(ne([Da,La,Sd,Td,Ua]))]),providerOptions:D.optional()}),Zd=P({role:z("tool"),content:_e(Ua),providerOptions:D.optional()});ne([zd,Ad,$d,Zd]);dt({prefix:"aitxt",size:24});(class extends TransformStream{constructor(){super({transform(e,t){t.enqueue(`data: ${JSON.stringify(e)}

`)},flush(e){e.enqueue(`data: [DONE]

`)}})}});var Fa=ne([q({type:z("text-start"),id:y(),providerMetadata:D.optional()}),q({type:z("text-delta"),id:y(),delta:y(),providerMetadata:D.optional()}),q({type:z("text-end"),id:y(),providerMetadata:D.optional()}),q({type:z("error"),errorText:y()}),q({type:z("tool-input-start"),toolCallId:y(),toolName:y(),providerExecuted:Q().optional(),dynamic:Q().optional()}),q({type:z("tool-input-delta"),toolCallId:y(),inputTextDelta:y()}),q({type:z("tool-input-available"),toolCallId:y(),toolName:y(),input:Y(),providerExecuted:Q().optional(),providerMetadata:D.optional(),dynamic:Q().optional()}),q({type:z("tool-input-error"),toolCallId:y(),toolName:y(),input:Y(),providerExecuted:Q().optional(),providerMetadata:D.optional(),dynamic:Q().optional(),errorText:y()}),q({type:z("tool-output-available"),toolCallId:y(),output:Y(),providerExecuted:Q().optional(),dynamic:Q().optional(),preliminary:Q().optional()}),q({type:z("tool-output-error"),toolCallId:y(),errorText:y(),providerExecuted:Q().optional(),dynamic:Q().optional()}),q({type:z("reasoning"),text:y(),providerMetadata:D.optional()}),q({type:z("reasoning-start"),id:y(),providerMetadata:D.optional()}),q({type:z("reasoning-delta"),id:y(),delta:y(),providerMetadata:D.optional()}),q({type:z("reasoning-end"),id:y(),providerMetadata:D.optional()}),q({type:z("reasoning-part-finish")}),q({type:z("source-url"),sourceId:y(),url:y(),title:y().optional(),providerMetadata:D.optional()}),q({type:z("source-document"),sourceId:y(),mediaType:y(),title:y(),filename:y().optional(),providerMetadata:D.optional()}),q({type:z("file"),url:y(),mediaType:y(),providerMetadata:D.optional()}),q({type:y().startsWith("data-"),id:y().optional(),data:Y(),transient:Q().optional()}),q({type:z("start-step")}),q({type:z("finish-step")}),q({type:z("start"),messageId:y().optional(),messageMetadata:Y().optional()}),q({type:z("finish"),messageMetadata:Y().optional()}),q({type:z("abort")}),q({type:z("message-metadata"),messageMetadata:Y()})]);function Od(e){return e.type.startsWith("data-")}function Va(e,t){if(e===void 0&&t===void 0)return;if(e===void 0)return t;if(t===void 0)return e;const r={...e};for(const n in t)if(Object.prototype.hasOwnProperty.call(t,n)){const a=t[n];if(a===void 0)continue;const s=n in e?e[n]:void 0,o=a!==null&&typeof a=="object"&&!Array.isArray(a)&&!(a instanceof Date)&&!(a instanceof RegExp),i=s!=null&&typeof s=="object"&&!Array.isArray(s)&&!(s instanceof Date)&&!(s instanceof RegExp);o&&i?r[n]=Va(s,a):r[n]=a}return r}function Nd(e){const t=["ROOT"];let r=-1,n=null;function a(u,c,d){switch(u){case'"':{r=c,t.pop(),t.push(d),t.push("INSIDE_STRING");break}case"f":case"t":case"n":{r=c,n=c,t.pop(),t.push(d),t.push("INSIDE_LITERAL");break}case"-":{t.pop(),t.push(d),t.push("INSIDE_NUMBER");break}case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":{r=c,t.pop(),t.push(d),t.push("INSIDE_NUMBER");break}case"{":{r=c,t.pop(),t.push(d),t.push("INSIDE_OBJECT_START");break}case"[":{r=c,t.pop(),t.push(d),t.push("INSIDE_ARRAY_START");break}}}function s(u,c){switch(u){case",":{t.pop(),t.push("INSIDE_OBJECT_AFTER_COMMA");break}case"}":{r=c,t.pop();break}}}function o(u,c){switch(u){case",":{t.pop(),t.push("INSIDE_ARRAY_AFTER_COMMA");break}case"]":{r=c,t.pop();break}}}for(let u=0;u<e.length;u++){const c=e[u];switch(t[t.length-1]){case"ROOT":a(c,u,"FINISH");break;case"INSIDE_OBJECT_START":{switch(c){case'"':{t.pop(),t.push("INSIDE_OBJECT_KEY");break}case"}":{r=u,t.pop();break}}break}case"INSIDE_OBJECT_AFTER_COMMA":{switch(c){case'"':{t.pop(),t.push("INSIDE_OBJECT_KEY");break}}break}case"INSIDE_OBJECT_KEY":{switch(c){case'"':{t.pop(),t.push("INSIDE_OBJECT_AFTER_KEY");break}}break}case"INSIDE_OBJECT_AFTER_KEY":{switch(c){case":":{t.pop(),t.push("INSIDE_OBJECT_BEFORE_VALUE");break}}break}case"INSIDE_OBJECT_BEFORE_VALUE":{a(c,u,"INSIDE_OBJECT_AFTER_VALUE");break}case"INSIDE_OBJECT_AFTER_VALUE":{s(c,u);break}case"INSIDE_STRING":{switch(c){case'"':{t.pop(),r=u;break}case"\\":{t.push("INSIDE_STRING_ESCAPE");break}default:r=u}break}case"INSIDE_ARRAY_START":{switch(c){case"]":{r=u,t.pop();break}default:{r=u,a(c,u,"INSIDE_ARRAY_AFTER_VALUE");break}}break}case"INSIDE_ARRAY_AFTER_VALUE":{switch(c){case",":{t.pop(),t.push("INSIDE_ARRAY_AFTER_COMMA");break}case"]":{r=u,t.pop();break}default:{r=u;break}}break}case"INSIDE_ARRAY_AFTER_COMMA":{a(c,u,"INSIDE_ARRAY_AFTER_VALUE");break}case"INSIDE_STRING_ESCAPE":{t.pop(),r=u;break}case"INSIDE_NUMBER":{switch(c){case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":{r=u;break}case"e":case"E":case"-":case".":break;case",":{t.pop(),t[t.length-1]==="INSIDE_ARRAY_AFTER_VALUE"&&o(c,u),t[t.length-1]==="INSIDE_OBJECT_AFTER_VALUE"&&s(c,u);break}case"}":{t.pop(),t[t.length-1]==="INSIDE_OBJECT_AFTER_VALUE"&&s(c,u);break}case"]":{t.pop(),t[t.length-1]==="INSIDE_ARRAY_AFTER_VALUE"&&o(c,u);break}default:{t.pop();break}}break}case"INSIDE_LITERAL":{const g=e.substring(n,u+1);!"false".startsWith(g)&&!"true".startsWith(g)&&!"null".startsWith(g)?(t.pop(),t[t.length-1]==="INSIDE_OBJECT_AFTER_VALUE"?s(c,u):t[t.length-1]==="INSIDE_ARRAY_AFTER_VALUE"&&o(c,u)):r=u;break}}}let i=e.slice(0,r+1);for(let u=t.length-1;u>=0;u--)switch(t[u]){case"INSIDE_STRING":{i+='"';break}case"INSIDE_OBJECT_KEY":case"INSIDE_OBJECT_AFTER_KEY":case"INSIDE_OBJECT_AFTER_COMMA":case"INSIDE_OBJECT_START":case"INSIDE_OBJECT_BEFORE_VALUE":case"INSIDE_OBJECT_AFTER_VALUE":{i+="}";break}case"INSIDE_ARRAY_START":case"INSIDE_ARRAY_AFTER_COMMA":case"INSIDE_ARRAY_AFTER_VALUE":{i+="]";break}case"INSIDE_LITERAL":{const d=e.substring(n,e.length);"true".startsWith(d)?i+="true".slice(d.length):"false".startsWith(d)?i+="false".slice(d.length):"null".startsWith(d)&&(i+="null".slice(d.length))}}return i}async function Ba(e){if(e===void 0)return{value:void 0,state:"undefined-input"};let t=await Pt({text:e});return t.success?{value:t.value,state:"successful-parse"}:(t=await Pt({text:Nd(e)}),t.success?{value:t.value,state:"repaired-parse"}:{value:void 0,state:"failed-parse"})}function Qe(e){return e.type.startsWith("tool-")}function En(e){return e.type.split("-").slice(1).join("-")}function Cd({lastMessage:e,messageId:t}){return{message:(e==null?void 0:e.role)==="assistant"?e:{id:t,metadata:void 0,role:"assistant",parts:[]},activeTextParts:{},activeReasoningParts:{},partialToolCalls:{}}}function Pd({stream:e,messageMetadataSchema:t,dataPartSchemas:r,runUpdateMessageJob:n,onError:a,onToolCall:s,onData:o}){return e.pipeThrough(new TransformStream({async transform(i,u){await n(async({state:c,write:d})=>{var g,x,A,b;function T(f){const _=c.message.parts.filter(Qe).find(I=>I.toolCallId===f);if(_==null)throw new Error("tool-output-error must be preceded by a tool-input-available");return _}function v(f){const _=c.message.parts.filter(I=>I.type==="dynamic-tool").find(I=>I.toolCallId===f);if(_==null)throw new Error("tool-output-error must be preceded by a tool-input-available");return _}function l(f){var S;const _=c.message.parts.find(de=>Qe(de)&&de.toolCallId===f.toolCallId),I=f,J=_;_!=null?(_.state=f.state,J.input=I.input,J.output=I.output,J.errorText=I.errorText,J.rawInput=I.rawInput,J.preliminary=I.preliminary,J.providerExecuted=(S=I.providerExecuted)!=null?S:_.providerExecuted,I.providerMetadata!=null&&_.state==="input-available"&&(_.callProviderMetadata=I.providerMetadata)):c.message.parts.push({type:`tool-${f.toolName}`,toolCallId:f.toolCallId,state:f.state,input:I.input,output:I.output,rawInput:I.rawInput,errorText:I.errorText,providerExecuted:I.providerExecuted,preliminary:I.preliminary,...I.providerMetadata!=null?{callProviderMetadata:I.providerMetadata}:{}})}function m(f){var S;const _=c.message.parts.find(de=>de.type==="dynamic-tool"&&de.toolCallId===f.toolCallId),I=f,J=_;_!=null?(_.state=f.state,J.toolName=f.toolName,J.input=I.input,J.output=I.output,J.errorText=I.errorText,J.rawInput=(S=I.rawInput)!=null?S:J.rawInput,J.preliminary=I.preliminary,I.providerMetadata!=null&&_.state==="input-available"&&(_.callProviderMetadata=I.providerMetadata)):c.message.parts.push({type:"dynamic-tool",toolName:f.toolName,toolCallId:f.toolCallId,state:f.state,input:I.input,output:I.output,errorText:I.errorText,preliminary:I.preliminary,...I.providerMetadata!=null?{callProviderMetadata:I.providerMetadata}:{}})}async function p(f){if(f!=null){const S=c.message.metadata!=null?Va(c.message.metadata,f):f;t!=null&&await Sn({value:S,schema:t}),c.message.metadata=S}}switch(i.type){case"text-start":{const f={type:"text",text:"",providerMetadata:i.providerMetadata,state:"streaming"};c.activeTextParts[i.id]=f,c.message.parts.push(f),d();break}case"text-delta":{const f=c.activeTextParts[i.id];f.text+=i.delta,f.providerMetadata=(g=i.providerMetadata)!=null?g:f.providerMetadata,d();break}case"text-end":{const f=c.activeTextParts[i.id];f.state="done",f.providerMetadata=(x=i.providerMetadata)!=null?x:f.providerMetadata,delete c.activeTextParts[i.id],d();break}case"reasoning-start":{const f={type:"reasoning",text:"",providerMetadata:i.providerMetadata,state:"streaming"};c.activeReasoningParts[i.id]=f,c.message.parts.push(f),d();break}case"reasoning-delta":{const f=c.activeReasoningParts[i.id];f.text+=i.delta,f.providerMetadata=(A=i.providerMetadata)!=null?A:f.providerMetadata,d();break}case"reasoning-end":{const f=c.activeReasoningParts[i.id];f.providerMetadata=(b=i.providerMetadata)!=null?b:f.providerMetadata,f.state="done",delete c.activeReasoningParts[i.id],d();break}case"file":{c.message.parts.push({type:"file",mediaType:i.mediaType,url:i.url}),d();break}case"source-url":{c.message.parts.push({type:"source-url",sourceId:i.sourceId,url:i.url,title:i.title,providerMetadata:i.providerMetadata}),d();break}case"source-document":{c.message.parts.push({type:"source-document",sourceId:i.sourceId,mediaType:i.mediaType,title:i.title,filename:i.filename,providerMetadata:i.providerMetadata}),d();break}case"tool-input-start":{const f=c.message.parts.filter(Qe);c.partialToolCalls[i.toolCallId]={text:"",toolName:i.toolName,index:f.length,dynamic:i.dynamic},i.dynamic?m({toolCallId:i.toolCallId,toolName:i.toolName,state:"input-streaming",input:void 0}):l({toolCallId:i.toolCallId,toolName:i.toolName,state:"input-streaming",input:void 0,providerExecuted:i.providerExecuted}),d();break}case"tool-input-delta":{const f=c.partialToolCalls[i.toolCallId];f.text+=i.inputTextDelta;const{value:S}=await Ba(f.text);f.dynamic?m({toolCallId:i.toolCallId,toolName:f.toolName,state:"input-streaming",input:S}):l({toolCallId:i.toolCallId,toolName:f.toolName,state:"input-streaming",input:S}),d();break}case"tool-input-available":{i.dynamic?m({toolCallId:i.toolCallId,toolName:i.toolName,state:"input-available",input:i.input,providerMetadata:i.providerMetadata}):l({toolCallId:i.toolCallId,toolName:i.toolName,state:"input-available",input:i.input,providerExecuted:i.providerExecuted,providerMetadata:i.providerMetadata}),d(),s&&!i.providerExecuted&&await s({toolCall:i});break}case"tool-input-error":{i.dynamic?m({toolCallId:i.toolCallId,toolName:i.toolName,state:"output-error",input:i.input,errorText:i.errorText,providerMetadata:i.providerMetadata}):l({toolCallId:i.toolCallId,toolName:i.toolName,state:"output-error",input:void 0,rawInput:i.input,errorText:i.errorText,providerExecuted:i.providerExecuted,providerMetadata:i.providerMetadata}),d();break}case"tool-output-available":{if(i.dynamic){const f=v(i.toolCallId);m({toolCallId:i.toolCallId,toolName:f.toolName,state:"output-available",input:f.input,output:i.output,preliminary:i.preliminary})}else{const f=T(i.toolCallId);l({toolCallId:i.toolCallId,toolName:En(f),state:"output-available",input:f.input,output:i.output,providerExecuted:i.providerExecuted,preliminary:i.preliminary})}d();break}case"tool-output-error":{if(i.dynamic){const f=v(i.toolCallId);m({toolCallId:i.toolCallId,toolName:f.toolName,state:"output-error",input:f.input,errorText:i.errorText})}else{const f=T(i.toolCallId);l({toolCallId:i.toolCallId,toolName:En(f),state:"output-error",input:f.input,rawInput:f.rawInput,errorText:i.errorText})}d();break}case"start-step":{c.message.parts.push({type:"step-start"});break}case"finish-step":{c.activeTextParts={},c.activeReasoningParts={};break}case"start":{i.messageId!=null&&(c.message.id=i.messageId),await p(i.messageMetadata),(i.messageId!=null||i.messageMetadata!=null)&&d();break}case"finish":{await p(i.messageMetadata),i.messageMetadata!=null&&d();break}case"message-metadata":{await p(i.messageMetadata),i.messageMetadata!=null&&d();break}case"error":{a==null||a(new Error(i.errorText));break}default:if(Od(i)){(r==null?void 0:r[i.type])!=null&&await Sn({value:i.data,schema:r[i.type]});const f=i;if(f.transient){o==null||o(f);break}const S=f.id!=null?c.message.parts.find(_=>f.type===_.type&&f.id===_.id):void 0;S!=null?S.data=f.data:c.message.parts.push(f),o==null||o(f),d()}}u.enqueue(i)})}}))}async function Ja({stream:e,onError:t}){const r=e.getReader();try{for(;;){const{done:n}=await r.read();if(n)break}}catch(n){t==null||t(n)}finally{r.releaseLock()}}dt({prefix:"aitxt",size:24});dt({prefix:"aiobj",size:24});function zn(e,t){if(e===t)return!0;if(e==null||t==null)return!1;if(typeof e!="object"&&typeof t!="object")return e===t;if(e.constructor!==t.constructor)return!1;if(e instanceof Date&&t instanceof Date)return e.getTime()===t.getTime();if(Array.isArray(e)){if(e.length!==t.length)return!1;for(let a=0;a<e.length;a++)if(!zn(e[a],t[a]))return!1;return!0}const r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(const a of r)if(!n.includes(a)||!zn(e[a],t[a]))return!1;return!0}var Rd=class{constructor(){this.queue=[],this.isProcessing=!1}async processQueue(){if(!this.isProcessing){for(this.isProcessing=!0;this.queue.length>0;)await this.queue[0](),this.queue.shift();this.isProcessing=!1}}async run(e){return new Promise((t,r)=>{this.queue.push(async()=>{try{await e(),t()}catch(n){r(n)}}),this.processQueue()})}};dt({prefix:"aiobj",size:24});var Md={};xd(Md,{object:()=>Dd,text:()=>jd});var jd=()=>({type:"text",responseFormat:{type:"text"},async parsePartial({text:e}){return{partial:e}},async parseOutput({text:e}){return e}}),Dd=({schema:e})=>{const t=bd(e);return{type:"object",responseFormat:{type:"json",schema:t.jsonSchema},async parsePartial({text:r}){const n=await Ba(r);switch(n.state){case"failed-parse":case"undefined-input":return;case"repaired-parse":case"successful-parse":return{partial:n.value};default:{const a=n.state;throw new Error(`Unsupported parse state: ${a}`)}}},async parseOutput({text:r},n){const a=await Pt({text:r});if(!a.success)throw new Tn({message:"No object generated: could not parse the response.",cause:a.error,text:r,response:n.response,usage:n.usage,finishReason:n.finishReason});const s=await kr({value:a.value,schema:t});if(!s.success)throw new Tn({message:"No object generated: response did not match schema.",cause:s.error,text:r,response:n.response,usage:n.usage,finishReason:n.finishReason});return s.value}}},Ld=De({name:y(),version:y()}),Sr=De({_meta:ee(P({}).loose())}),it=Sr,Ud=P({method:y(),params:ee(Sr)}),Fd=De({experimental:ee(P({}).loose()),logging:ee(P({}).loose()),prompts:ee(De({listChanged:ee(Q())})),resources:ee(De({subscribe:ee(Q()),listChanged:ee(Q())})),tools:ee(De({listChanged:ee(Q())}))});it.extend({protocolVersion:y(),capabilities:Fd,serverInfo:Ld,instructions:ee(y())});var Vd=it.extend({nextCursor:ee(y())}),Bd=P({name:y(),description:ee(y()),inputSchema:P({type:z("object"),properties:ee(P({}).loose())}).loose()}).loose();Vd.extend({tools:_e(Bd)});var Jd=P({type:z("text"),text:y()}).loose(),Gd=P({type:z("image"),data:wa(),mimeType:y()}).loose(),Ga=P({uri:y(),mimeType:ee(y())}).loose(),Wd=Ga.extend({text:y()}),qd=Ga.extend({blob:wa()}),Yd=P({type:z("resource"),resource:ne([Wd,qd])}).loose();it.extend({content:_e(ne([Jd,Gd,Yd])),isError:Q().default(!1).optional()}).or(it.extend({toolResult:Y()}));var Dt="2.0",Kd=P({jsonrpc:z(Dt),id:ne([y(),rt().int()])}).merge(Ud).strict(),Hd=P({jsonrpc:z(Dt),id:ne([y(),rt().int()]),result:it}).strict(),Xd=P({jsonrpc:z(Dt),id:ne([y(),rt().int()]),error:P({code:rt().int(),message:y(),data:ee(Y())})}).strict(),Qd=P({jsonrpc:z(Dt)}).merge(P({method:y(),params:ee(Sr)})).strict();ne([Kd,Qd,Hd,Xd]);async function ef({stream:e,onTextPart:t}){const r=e.pipeThrough(new TextDecoderStream).getReader();for(;;){const{done:n,value:a}=await r.read();if(n)break;await t(a)}}var tf=()=>fetch;async function cp({api:e,prompt:t,credentials:r,headers:n,body:a,streamProtocol:s="data",setCompletion:o,setLoading:i,setError:u,setAbortController:c,onFinish:d,onError:g,fetch:x=tf()}){var A;try{i(!0),u(void 0);const b=new AbortController;c(b),o("");const T=await x(e,{method:"POST",body:JSON.stringify({prompt:t,...a}),credentials:r,headers:{"Content-Type":"application/json",...n},signal:b.signal}).catch(l=>{throw l});if(!T.ok)throw new Error((A=await T.text())!=null?A:"Failed to fetch the chat response.");if(!T.body)throw new Error("The response body is empty.");let v="";switch(s){case"text":{await ef({stream:T.body,onTextPart:l=>{v+=l,o(v)}});break}case"data":{await Ja({stream:Ca({stream:T.body,schema:Fa}).pipeThrough(new TransformStream({async transform(l){if(!l.success)throw l.error;const m=l.value;if(m.type==="text-delta")v+=m.delta,o(v);else if(m.type==="error")throw new Error(m.errorText)}})),onError:l=>{throw l}});break}default:{const l=s;throw new Error(`Unknown stream protocol: ${l}`)}}return d&&d(t,v),c(null),v}catch(b){if(b.name==="AbortError")return c(null),null;b instanceof Error&&g&&g(b),u(b)}finally{i(!1)}}async function rf(e){if(e==null)return[];if(!globalThis.FileList||!(e instanceof globalThis.FileList))throw new Error("FileList is not supported in the current environment");return Promise.all(Array.from(e).map(async t=>{const{name:r,type:n}=t,a=await new Promise((s,o)=>{const i=new FileReader;i.onload=u=>{var c;s((c=u.target)==null?void 0:c.result)},i.onerror=u=>o(u),i.readAsDataURL(t)});return{type:"file",mediaType:n,filename:r,url:a}}))}var nf=class{constructor({api:e="/api/chat",credentials:t,headers:r,body:n,fetch:a,prepareSendMessagesRequest:s,prepareReconnectToStreamRequest:o}){this.api=e,this.credentials=t,this.headers=r,this.body=n,this.fetch=a,this.prepareSendMessagesRequest=s,this.prepareReconnectToStreamRequest=o}async sendMessages({abortSignal:e,...t}){var r,n,a,s,o;const i=await Re(this.body),u=await Re(this.headers),c=await Re(this.credentials),d=await((r=this.prepareSendMessagesRequest)==null?void 0:r.call(this,{api:this.api,id:t.chatId,messages:t.messages,body:{...i,...t.body},headers:{...u,...t.headers},credentials:c,requestMetadata:t.metadata,trigger:t.trigger,messageId:t.messageId})),g=(n=d==null?void 0:d.api)!=null?n:this.api,x=(d==null?void 0:d.headers)!==void 0?d.headers:{...u,...t.headers},A=(d==null?void 0:d.body)!==void 0?d.body:{...i,...t.body,id:t.chatId,messages:t.messages,trigger:t.trigger,messageId:t.messageId},b=(a=d==null?void 0:d.credentials)!=null?a:c,v=await((s=this.fetch)!=null?s:globalThis.fetch)(g,{method:"POST",headers:{"Content-Type":"application/json",...x},body:JSON.stringify(A),credentials:b,signal:e});if(!v.ok)throw new Error((o=await v.text())!=null?o:"Failed to fetch the chat response.");if(!v.body)throw new Error("The response body is empty.");return this.processResponseStream(v.body)}async reconnectToStream(e){var t,r,n,a,s;const o=await Re(this.body),i=await Re(this.headers),u=await Re(this.credentials),c=await((t=this.prepareReconnectToStreamRequest)==null?void 0:t.call(this,{api:this.api,id:e.chatId,body:{...o,...e.body},headers:{...i,...e.headers},credentials:u,requestMetadata:e.metadata})),d=(r=c==null?void 0:c.api)!=null?r:`${this.api}/${e.chatId}/stream`,g=(c==null?void 0:c.headers)!==void 0?c.headers:{...i,...e.headers},x=(n=c==null?void 0:c.credentials)!=null?n:u,b=await((a=this.fetch)!=null?a:globalThis.fetch)(d,{method:"GET",headers:g,credentials:x});if(b.status===204)return null;if(!b.ok)throw new Error((s=await b.text())!=null?s:"Failed to fetch the chat response.");if(!b.body)throw new Error("The response body is empty.");return this.processResponseStream(b.body)}},af=class extends nf{constructor(e={}){super(e)}processResponseStream(e){return Ca({stream:e,schema:Fa}).pipeThrough(new TransformStream({async transform(t,r){if(!t.success)throw t.error;r.enqueue(t.value)}}))}},up=class{constructor({generateId:e=sd,id:t=e(),transport:r=new af,messageMetadataSchema:n,dataPartSchemas:a,state:s,onError:o,onToolCall:i,onFinish:u,onData:c,sendAutomaticallyWhen:d}){this.activeResponse=void 0,this.jobExecutor=new Rd,this.sendMessage=async(g,x)=>{var A,b,T,v;if(g==null){await this.makeRequest({trigger:"submit-message",messageId:(A=this.lastMessage)==null?void 0:A.id,...x});return}let l;if("text"in g||"files"in g?l={parts:[...Array.isArray(g.files)?g.files:await rf(g.files),..."text"in g&&g.text!=null?[{type:"text",text:g.text}]:[]]}:l=g,g.messageId!=null){const m=this.state.messages.findIndex(p=>p.id===g.messageId);if(m===-1)throw new Error(`message with id ${g.messageId} not found`);if(this.state.messages[m].role!=="user")throw new Error(`message with id ${g.messageId} is not a user message`);this.state.messages=this.state.messages.slice(0,m+1),this.state.replaceMessage(m,{...l,id:g.messageId,role:(b=l.role)!=null?b:"user",metadata:g.metadata})}else this.state.pushMessage({...l,id:(T=l.id)!=null?T:this.generateId(),role:(v=l.role)!=null?v:"user",metadata:g.metadata});await this.makeRequest({trigger:"submit-message",messageId:g.messageId,...x})},this.regenerate=async({messageId:g,...x}={})=>{const A=g==null?this.state.messages.length-1:this.state.messages.findIndex(b=>b.id===g);if(A===-1)throw new Error(`message ${g} not found`);this.state.messages=this.state.messages.slice(0,this.messages[A].role==="assistant"?A:A+1),await this.makeRequest({trigger:"regenerate-message",messageId:g,...x})},this.resumeStream=async(g={})=>{await this.makeRequest({trigger:"resume-stream",...g})},this.clearError=()=>{this.status==="error"&&(this.state.error=void 0,this.setStatus({status:"ready"}))},this.addToolResult=async({tool:g,toolCallId:x,output:A})=>this.jobExecutor.run(async()=>{var b,T;const v=this.state.messages,l=v[v.length-1];this.state.replaceMessage(v.length-1,{...l,parts:l.parts.map(m=>Qe(m)&&m.toolCallId===x?{...m,state:"output-available",output:A}:m)}),this.activeResponse&&(this.activeResponse.state.message.parts=this.activeResponse.state.message.parts.map(m=>Qe(m)&&m.toolCallId===x?{...m,state:"output-available",output:A,errorText:void 0}:m)),this.status!=="streaming"&&this.status!=="submitted"&&((b=this.sendAutomaticallyWhen)!=null&&b.call(this,{messages:this.state.messages}))&&this.makeRequest({trigger:"submit-message",messageId:(T=this.lastMessage)==null?void 0:T.id})}),this.stop=async()=>{var g;this.status!=="streaming"&&this.status!=="submitted"||(g=this.activeResponse)!=null&&g.abortController&&this.activeResponse.abortController.abort()},this.id=t,this.transport=r,this.generateId=e,this.messageMetadataSchema=n,this.dataPartSchemas=a,this.state=s,this.onError=o,this.onToolCall=i,this.onFinish=u,this.onData=c,this.sendAutomaticallyWhen=d}get status(){return this.state.status}setStatus({status:e,error:t}){this.status!==e&&(this.state.status=e,this.state.error=t)}get error(){return this.state.error}get messages(){return this.state.messages}get lastMessage(){return this.state.messages[this.state.messages.length-1]}set messages(e){this.state.messages=e}async makeRequest({trigger:e,metadata:t,headers:r,body:n,messageId:a}){var s,o,i;this.setStatus({status:"submitted",error:void 0});const u=this.lastMessage;try{const c={state:Cd({lastMessage:this.state.snapshot(u),messageId:this.generateId()}),abortController:new AbortController};this.activeResponse=c;let d;if(e==="resume-stream"){const x=await this.transport.reconnectToStream({chatId:this.id,metadata:t,headers:r,body:n});if(x==null){this.setStatus({status:"ready"});return}d=x}else d=await this.transport.sendMessages({chatId:this.id,messages:this.state.messages,abortSignal:c.abortController.signal,metadata:t,headers:r,body:n,trigger:e,messageId:a});const g=x=>this.jobExecutor.run(()=>x({state:c.state,write:()=>{var A;this.setStatus({status:"streaming"}),c.state.message.id===((A=this.lastMessage)==null?void 0:A.id)?this.state.replaceMessage(this.state.messages.length-1,c.state.message):this.state.pushMessage(c.state.message)}}));await Ja({stream:Pd({stream:d,onToolCall:this.onToolCall,onData:this.onData,messageMetadataSchema:this.messageMetadataSchema,dataPartSchemas:this.dataPartSchemas,runUpdateMessageJob:g,onError:x=>{throw x}}),onError:x=>{throw x}}),(s=this.onFinish)==null||s.call(this,{message:c.state.message}),this.setStatus({status:"ready"})}catch(c){if(c.name==="AbortError")return this.setStatus({status:"ready"}),null;this.onError&&c instanceof Error&&this.onError(c),this.setStatus({status:"error",error:c})}finally{this.activeResponse=void 0}(o=this.sendAutomaticallyWhen)!=null&&o.call(this,{messages:this.state.messages})&&await this.makeRequest({trigger:"submit-message",messageId:(i=this.lastMessage)==null?void 0:i.id,metadata:t,headers:r,body:n})}},sf=P({type:z("text"),text:y(),state:jt(["streaming","done"]).optional(),providerMetadata:D.optional()}),of=P({type:z("reasoning"),text:y(),state:jt(["streaming","done"]).optional(),providerMetadata:D.optional()}),cf=P({type:z("source-url"),sourceId:y(),url:y(),title:y().optional(),providerMetadata:D.optional()}),uf=P({type:z("source-document"),sourceId:y(),mediaType:y(),title:y(),filename:y().optional(),providerMetadata:D.optional()}),lf=P({type:z("file"),mediaType:y(),filename:y().optional(),url:y(),providerMetadata:D.optional()}),df=P({type:z("step-start")}),ff=P({type:y().startsWith("data-"),id:y().optional(),data:Y()}),pf=[P({type:z("dynamic-tool"),toolName:y(),toolCallId:y(),state:z("input-streaming"),input:Y().optional(),output:ie().optional(),errorText:ie().optional()}),P({type:z("dynamic-tool"),toolName:y(),toolCallId:y(),state:z("input-available"),input:Y(),output:ie().optional(),errorText:ie().optional(),callProviderMetadata:D.optional()}),P({type:z("dynamic-tool"),toolName:y(),toolCallId:y(),state:z("output-available"),input:Y(),output:Y(),errorText:ie().optional(),callProviderMetadata:D.optional(),preliminary:Q().optional()}),P({type:z("dynamic-tool"),toolName:y(),toolCallId:y(),state:z("output-error"),input:Y(),output:ie().optional(),errorText:y(),callProviderMetadata:D.optional()})],hf=[P({type:y().startsWith("tool-"),toolCallId:y(),state:z("input-streaming"),input:Y().optional(),output:ie().optional(),errorText:ie().optional()}),P({type:y().startsWith("tool-"),toolCallId:y(),state:z("input-available"),input:Y(),output:ie().optional(),errorText:ie().optional(),callProviderMetadata:D.optional()}),P({type:y().startsWith("tool-"),toolCallId:y(),state:z("output-available"),input:Y(),output:Y(),errorText:ie().optional(),callProviderMetadata:D.optional(),preliminary:Q().optional()}),P({type:y().startsWith("tool-"),toolCallId:y(),state:z("output-error"),input:Y(),output:ie().optional(),errorText:y(),callProviderMetadata:D.optional()})];P({id:y(),role:jt(["system","user","assistant"]),metadata:Y().optional(),parts:_e(ne([sf,of,cf,uf,lf,df,ff,...pf,...hf]))});const Tr="-",mf=e=>{const t=vf(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:o=>{const i=o.split(Tr);return i[0]===""&&i.length!==1&&i.shift(),Wa(i,t)||gf(o)},getConflictingClassGroupIds:(o,i)=>{const u=r[o]||[];return i&&n[o]?[...u,...n[o]]:u}}},Wa=(e,t)=>{var o;if(e.length===0)return t.classGroupId;const r=e[0],n=t.nextPart.get(r),a=n?Wa(e.slice(1),n):void 0;if(a)return a;if(t.validators.length===0)return;const s=e.join(Tr);return(o=t.validators.find(({validator:i})=>i(s)))==null?void 0:o.classGroupId},An=/^\[(.+)\]$/,gf=e=>{if(An.test(e)){const t=An.exec(e)[1],r=t==null?void 0:t.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},vf=e=>{const{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(const a in r)lr(r[a],n,a,t);return n},lr=(e,t,r,n)=>{e.forEach(a=>{if(typeof a=="string"){const s=a===""?t:$n(t,a);s.classGroupId=r;return}if(typeof a=="function"){if(yf(a)){lr(a(n),t,r,n);return}t.validators.push({validator:a,classGroupId:r});return}Object.entries(a).forEach(([s,o])=>{lr(o,$n(t,s),r,n)})})},$n=(e,t)=>{let r=e;return t.split(Tr).forEach(n=>{r.nextPart.has(n)||r.nextPart.set(n,{nextPart:new Map,validators:[]}),r=r.nextPart.get(n)}),r},yf=e=>e.isThemeGetter,_f=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,n=new Map;const a=(s,o)=>{r.set(s,o),t++,t>e&&(t=0,n=r,r=new Map)};return{get(s){let o=r.get(s);if(o!==void 0)return o;if((o=n.get(s))!==void 0)return a(s,o),o},set(s,o){r.has(s)?r.set(s,o):a(s,o)}}},dr="!",fr=":",bf=fr.length,wf=e=>{const{prefix:t,experimentalParseClassName:r}=e;let n=a=>{const s=[];let o=0,i=0,u=0,c;for(let b=0;b<a.length;b++){let T=a[b];if(o===0&&i===0){if(T===fr){s.push(a.slice(u,b)),u=b+bf;continue}if(T==="/"){c=b;continue}}T==="["?o++:T==="]"?o--:T==="("?i++:T===")"&&i--}const d=s.length===0?a:a.substring(u),g=xf(d),x=g!==d,A=c&&c>u?c-u:void 0;return{modifiers:s,hasImportantModifier:x,baseClassName:g,maybePostfixModifierPosition:A}};if(t){const a=t+fr,s=n;n=o=>o.startsWith(a)?s(o.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:o,maybePostfixModifierPosition:void 0}}if(r){const a=n;n=s=>r({className:s,parseClassName:a})}return n},xf=e=>e.endsWith(dr)?e.substring(0,e.length-1):e.startsWith(dr)?e.substring(1):e,kf=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(n=>[n,!0]));return n=>{if(n.length<=1)return n;const a=[];let s=[];return n.forEach(o=>{o[0]==="["||t[o]?(a.push(...s.sort(),o),s=[]):s.push(o)}),a.push(...s.sort()),a}},If=e=>({cache:_f(e.cacheSize),parseClassName:wf(e),sortModifiers:kf(e),...mf(e)}),Sf=/\s+/,Tf=(e,t)=>{const{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:a,sortModifiers:s}=t,o=[],i=e.trim().split(Sf);let u="";for(let c=i.length-1;c>=0;c-=1){const d=i[c],{isExternal:g,modifiers:x,hasImportantModifier:A,baseClassName:b,maybePostfixModifierPosition:T}=r(d);if(g){u=d+(u.length>0?" "+u:u);continue}let v=!!T,l=n(v?b.substring(0,T):b);if(!l){if(!v){u=d+(u.length>0?" "+u:u);continue}if(l=n(b),!l){u=d+(u.length>0?" "+u:u);continue}v=!1}const m=s(x).join(":"),p=A?m+dr:m,f=p+l;if(o.includes(f))continue;o.push(f);const S=a(l,v);for(let _=0;_<S.length;++_){const I=S[_];o.push(p+I)}u=d+(u.length>0?" "+u:u)}return u};function Ef(){let e=0,t,r,n="";for(;e<arguments.length;)(t=arguments[e++])&&(r=qa(t))&&(n&&(n+=" "),n+=r);return n}const qa=e=>{if(typeof e=="string")return e;let t,r="";for(let n=0;n<e.length;n++)e[n]&&(t=qa(e[n]))&&(r&&(r+=" "),r+=t);return r};function pr(e,...t){let r,n,a,s=o;function o(u){const c=t.reduce((d,g)=>g(d),e());return r=If(c),n=r.cache.get,a=r.cache.set,s=i,i(u)}function i(u){const c=n(u);if(c)return c;const d=Tf(u,r);return a(u,d),d}return function(){return s(Ef.apply(null,arguments))}}const ae=e=>{const t=r=>r[e]||[];return t.isThemeGetter=!0,t},Ya=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Ka=/^\((?:(\w[\w-]*):)?(.+)\)$/i,zf=/^\d+\/\d+$/,Af=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,$f=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Zf=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Of=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Nf=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Me=e=>zf.test(e),j=e=>!!e&&!Number.isNaN(Number(e)),ze=e=>!!e&&Number.isInteger(Number(e)),Zn=e=>e.endsWith("%")&&j(e.slice(0,-1)),be=e=>Af.test(e),Cf=()=>!0,Pf=e=>$f.test(e)&&!Zf.test(e),Er=()=>!1,Rf=e=>Of.test(e),Mf=e=>Nf.test(e),jf=e=>!$(e)&&!Z(e),Df=e=>Be(e,Qa,Er),$=e=>Ya.test(e),Ae=e=>Be(e,es,Pf),Qt=e=>Be(e,Kf,j),Lf=e=>Be(e,Ha,Er),Uf=e=>Be(e,Xa,Mf),Ff=e=>Be(e,Er,Rf),Z=e=>Ka.test(e),bt=e=>Je(e,es),Vf=e=>Je(e,Hf),Bf=e=>Je(e,Ha),Jf=e=>Je(e,Qa),Gf=e=>Je(e,Xa),Wf=e=>Je(e,Xf,!0),Be=(e,t,r)=>{const n=Ya.exec(e);return n?n[1]?t(n[1]):r(n[2]):!1},Je=(e,t,r=!1)=>{const n=Ka.exec(e);return n?n[1]?t(n[1]):r:!1},Ha=e=>e==="position",qf=new Set(["image","url"]),Xa=e=>qf.has(e),Yf=new Set(["length","size","percentage"]),Qa=e=>Yf.has(e),es=e=>e==="length",Kf=e=>e==="number",Hf=e=>e==="family-name",Xf=e=>e==="shadow",hr=()=>{const e=ae("color"),t=ae("font"),r=ae("text"),n=ae("font-weight"),a=ae("tracking"),s=ae("leading"),o=ae("breakpoint"),i=ae("container"),u=ae("spacing"),c=ae("radius"),d=ae("shadow"),g=ae("inset-shadow"),x=ae("drop-shadow"),A=ae("blur"),b=ae("perspective"),T=ae("aspect"),v=ae("ease"),l=ae("animate"),m=()=>["auto","avoid","all","avoid-page","page","left","right","column"],p=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],f=()=>["auto","hidden","clip","visible","scroll"],S=()=>["auto","contain","none"],_=()=>[Z,$,u],I=()=>[Me,"full","auto",..._()],J=()=>[ze,"none","subgrid",Z,$],de=()=>["auto",{span:["full",ze,Z,$]},Z,$],ft=()=>[ze,"auto",Z,$],zr=()=>["auto","min","max","fr",Z,$],Lt=()=>["start","end","center","between","around","evenly","stretch","baseline"],Pe=()=>["start","end","center","stretch"],ge=()=>["auto",..._()],Ee=()=>[Me,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",..._()],G=()=>[e,Z,$],Ut=()=>[Zn,Ae],se=()=>["","none","full",c,Z,$],ue=()=>["",j,bt,Ae],pt=()=>["solid","dashed","dotted","double"],Ar=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],$r=()=>["","none",A,Z,$],Zr=()=>["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",Z,$],ht=()=>["none",j,Z,$],mt=()=>["none",j,Z,$],Ft=()=>[j,Z,$],gt=()=>[Me,"full",..._()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[be],breakpoint:[be],color:[Cf],container:[be],"drop-shadow":[be],ease:["in","out","in-out"],font:[jf],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[be],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[be],shadow:[be],spacing:["px",j],text:[be],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Me,$,Z,T]}],container:["container"],columns:[{columns:[j,$,Z,i]}],"break-after":[{"break-after":m()}],"break-before":[{"break-before":m()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...p(),$,Z]}],overflow:[{overflow:f()}],"overflow-x":[{"overflow-x":f()}],"overflow-y":[{"overflow-y":f()}],overscroll:[{overscroll:S()}],"overscroll-x":[{"overscroll-x":S()}],"overscroll-y":[{"overscroll-y":S()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:I()}],"inset-x":[{"inset-x":I()}],"inset-y":[{"inset-y":I()}],start:[{start:I()}],end:[{end:I()}],top:[{top:I()}],right:[{right:I()}],bottom:[{bottom:I()}],left:[{left:I()}],visibility:["visible","invisible","collapse"],z:[{z:[ze,"auto",Z,$]}],basis:[{basis:[Me,"full","auto",i,..._()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[j,Me,"auto","initial","none",$]}],grow:[{grow:["",j,Z,$]}],shrink:[{shrink:["",j,Z,$]}],order:[{order:[ze,"first","last","none",Z,$]}],"grid-cols":[{"grid-cols":J()}],"col-start-end":[{col:de()}],"col-start":[{"col-start":ft()}],"col-end":[{"col-end":ft()}],"grid-rows":[{"grid-rows":J()}],"row-start-end":[{row:de()}],"row-start":[{"row-start":ft()}],"row-end":[{"row-end":ft()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":zr()}],"auto-rows":[{"auto-rows":zr()}],gap:[{gap:_()}],"gap-x":[{"gap-x":_()}],"gap-y":[{"gap-y":_()}],"justify-content":[{justify:[...Lt(),"normal"]}],"justify-items":[{"justify-items":[...Pe(),"normal"]}],"justify-self":[{"justify-self":["auto",...Pe()]}],"align-content":[{content:["normal",...Lt()]}],"align-items":[{items:[...Pe(),"baseline"]}],"align-self":[{self:["auto",...Pe(),"baseline"]}],"place-content":[{"place-content":Lt()}],"place-items":[{"place-items":[...Pe(),"baseline"]}],"place-self":[{"place-self":["auto",...Pe()]}],p:[{p:_()}],px:[{px:_()}],py:[{py:_()}],ps:[{ps:_()}],pe:[{pe:_()}],pt:[{pt:_()}],pr:[{pr:_()}],pb:[{pb:_()}],pl:[{pl:_()}],m:[{m:ge()}],mx:[{mx:ge()}],my:[{my:ge()}],ms:[{ms:ge()}],me:[{me:ge()}],mt:[{mt:ge()}],mr:[{mr:ge()}],mb:[{mb:ge()}],ml:[{ml:ge()}],"space-x":[{"space-x":_()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":_()}],"space-y-reverse":["space-y-reverse"],size:[{size:Ee()}],w:[{w:[i,"screen",...Ee()]}],"min-w":[{"min-w":[i,"screen","none",...Ee()]}],"max-w":[{"max-w":[i,"screen","none","prose",{screen:[o]},...Ee()]}],h:[{h:["screen",...Ee()]}],"min-h":[{"min-h":["screen","none",...Ee()]}],"max-h":[{"max-h":["screen",...Ee()]}],"font-size":[{text:["base",r,bt,Ae]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,Z,Qt]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Zn,$]}],"font-family":[{font:[Vf,$,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,Z,$]}],"line-clamp":[{"line-clamp":[j,"none",Z,Qt]}],leading:[{leading:[s,..._()]}],"list-image":[{"list-image":["none",Z,$]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Z,$]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:G()}],"text-color":[{text:G()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...pt(),"wavy"]}],"text-decoration-thickness":[{decoration:[j,"from-font","auto",Z,Ae]}],"text-decoration-color":[{decoration:G()}],"underline-offset":[{"underline-offset":[j,"auto",Z,$]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:_()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Z,$]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Z,$]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...p(),Bf,Lf]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:["auto","cover","contain",Jf,Df]}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},ze,Z,$],radial:["",Z,$],conic:[ze,Z,$]},Gf,Uf]}],"bg-color":[{bg:G()}],"gradient-from-pos":[{from:Ut()}],"gradient-via-pos":[{via:Ut()}],"gradient-to-pos":[{to:Ut()}],"gradient-from":[{from:G()}],"gradient-via":[{via:G()}],"gradient-to":[{to:G()}],rounded:[{rounded:se()}],"rounded-s":[{"rounded-s":se()}],"rounded-e":[{"rounded-e":se()}],"rounded-t":[{"rounded-t":se()}],"rounded-r":[{"rounded-r":se()}],"rounded-b":[{"rounded-b":se()}],"rounded-l":[{"rounded-l":se()}],"rounded-ss":[{"rounded-ss":se()}],"rounded-se":[{"rounded-se":se()}],"rounded-ee":[{"rounded-ee":se()}],"rounded-es":[{"rounded-es":se()}],"rounded-tl":[{"rounded-tl":se()}],"rounded-tr":[{"rounded-tr":se()}],"rounded-br":[{"rounded-br":se()}],"rounded-bl":[{"rounded-bl":se()}],"border-w":[{border:ue()}],"border-w-x":[{"border-x":ue()}],"border-w-y":[{"border-y":ue()}],"border-w-s":[{"border-s":ue()}],"border-w-e":[{"border-e":ue()}],"border-w-t":[{"border-t":ue()}],"border-w-r":[{"border-r":ue()}],"border-w-b":[{"border-b":ue()}],"border-w-l":[{"border-l":ue()}],"divide-x":[{"divide-x":ue()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ue()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...pt(),"hidden","none"]}],"divide-style":[{divide:[...pt(),"hidden","none"]}],"border-color":[{border:G()}],"border-color-x":[{"border-x":G()}],"border-color-y":[{"border-y":G()}],"border-color-s":[{"border-s":G()}],"border-color-e":[{"border-e":G()}],"border-color-t":[{"border-t":G()}],"border-color-r":[{"border-r":G()}],"border-color-b":[{"border-b":G()}],"border-color-l":[{"border-l":G()}],"divide-color":[{divide:G()}],"outline-style":[{outline:[...pt(),"none","hidden"]}],"outline-offset":[{"outline-offset":[j,Z,$]}],"outline-w":[{outline:["",j,bt,Ae]}],"outline-color":[{outline:[e]}],shadow:[{shadow:["","none",d,Wf,Ff]}],"shadow-color":[{shadow:G()}],"inset-shadow":[{"inset-shadow":["none",Z,$,g]}],"inset-shadow-color":[{"inset-shadow":G()}],"ring-w":[{ring:ue()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:G()}],"ring-offset-w":[{"ring-offset":[j,Ae]}],"ring-offset-color":[{"ring-offset":G()}],"inset-ring-w":[{"inset-ring":ue()}],"inset-ring-color":[{"inset-ring":G()}],opacity:[{opacity:[j,Z,$]}],"mix-blend":[{"mix-blend":[...Ar(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Ar()}],filter:[{filter:["","none",Z,$]}],blur:[{blur:$r()}],brightness:[{brightness:[j,Z,$]}],contrast:[{contrast:[j,Z,$]}],"drop-shadow":[{"drop-shadow":["","none",x,Z,$]}],grayscale:[{grayscale:["",j,Z,$]}],"hue-rotate":[{"hue-rotate":[j,Z,$]}],invert:[{invert:["",j,Z,$]}],saturate:[{saturate:[j,Z,$]}],sepia:[{sepia:["",j,Z,$]}],"backdrop-filter":[{"backdrop-filter":["","none",Z,$]}],"backdrop-blur":[{"backdrop-blur":$r()}],"backdrop-brightness":[{"backdrop-brightness":[j,Z,$]}],"backdrop-contrast":[{"backdrop-contrast":[j,Z,$]}],"backdrop-grayscale":[{"backdrop-grayscale":["",j,Z,$]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[j,Z,$]}],"backdrop-invert":[{"backdrop-invert":["",j,Z,$]}],"backdrop-opacity":[{"backdrop-opacity":[j,Z,$]}],"backdrop-saturate":[{"backdrop-saturate":[j,Z,$]}],"backdrop-sepia":[{"backdrop-sepia":["",j,Z,$]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":_()}],"border-spacing-x":[{"border-spacing-x":_()}],"border-spacing-y":[{"border-spacing-y":_()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Z,$]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[j,"initial",Z,$]}],ease:[{ease:["linear","initial",v,Z,$]}],delay:[{delay:[j,Z,$]}],animate:[{animate:["none",l,Z,$]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[b,Z,$]}],"perspective-origin":[{"perspective-origin":Zr()}],rotate:[{rotate:ht()}],"rotate-x":[{"rotate-x":ht()}],"rotate-y":[{"rotate-y":ht()}],"rotate-z":[{"rotate-z":ht()}],scale:[{scale:mt()}],"scale-x":[{"scale-x":mt()}],"scale-y":[{"scale-y":mt()}],"scale-z":[{"scale-z":mt()}],"scale-3d":["scale-3d"],skew:[{skew:Ft()}],"skew-x":[{"skew-x":Ft()}],"skew-y":[{"skew-y":Ft()}],transform:[{transform:[Z,$,"","none","gpu","cpu"]}],"transform-origin":[{origin:Zr()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:gt()}],"translate-x":[{"translate-x":gt()}],"translate-y":[{"translate-y":gt()}],"translate-z":[{"translate-z":gt()}],"translate-none":["translate-none"],accent:[{accent:G()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:G()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Z,$]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":_()}],"scroll-mx":[{"scroll-mx":_()}],"scroll-my":[{"scroll-my":_()}],"scroll-ms":[{"scroll-ms":_()}],"scroll-me":[{"scroll-me":_()}],"scroll-mt":[{"scroll-mt":_()}],"scroll-mr":[{"scroll-mr":_()}],"scroll-mb":[{"scroll-mb":_()}],"scroll-ml":[{"scroll-ml":_()}],"scroll-p":[{"scroll-p":_()}],"scroll-px":[{"scroll-px":_()}],"scroll-py":[{"scroll-py":_()}],"scroll-ps":[{"scroll-ps":_()}],"scroll-pe":[{"scroll-pe":_()}],"scroll-pt":[{"scroll-pt":_()}],"scroll-pr":[{"scroll-pr":_()}],"scroll-pb":[{"scroll-pb":_()}],"scroll-pl":[{"scroll-pl":_()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Z,$]}],fill:[{fill:["none",...G()]}],"stroke-w":[{stroke:[j,bt,Ae,Qt]}],stroke:[{stroke:["none",...G()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["before","after","placeholder","file","marker","selection","first-line","first-letter","backdrop","*","**"]}},Qf=(e,{cacheSize:t,prefix:r,experimentalParseClassName:n,extend:a={},override:s={}})=>(He(e,"cacheSize",t),He(e,"prefix",r),He(e,"experimentalParseClassName",n),wt(e.theme,s.theme),wt(e.classGroups,s.classGroups),wt(e.conflictingClassGroups,s.conflictingClassGroups),wt(e.conflictingClassGroupModifiers,s.conflictingClassGroupModifiers),He(e,"orderSensitiveModifiers",s.orderSensitiveModifiers),xt(e.theme,a.theme),xt(e.classGroups,a.classGroups),xt(e.conflictingClassGroups,a.conflictingClassGroups),xt(e.conflictingClassGroupModifiers,a.conflictingClassGroupModifiers),ts(e,a,"orderSensitiveModifiers"),e),He=(e,t,r)=>{r!==void 0&&(e[t]=r)},wt=(e,t)=>{if(t)for(const r in t)He(e,r,t[r])},xt=(e,t)=>{if(t)for(const r in t)ts(e,t,r)},ts=(e,t,r)=>{const n=t[r];n!==void 0&&(e[r]=e[r]?e[r].concat(n):n)},lp=(e,...t)=>typeof e=="function"?pr(hr,e,...t):pr(()=>Qf(hr(),e),...t),ep=pr(hr);function tp(...e){return ep(Nn(e))}var rp=rs("<textarea></textarea>");function dp(e,t){gs(t,!0);let r=Nr(t,"ref",15,null),n=Nr(t,"value",15),a=zs(t,["$$slots","$$events","$$legacy","ref","value","class"]);var s=rp();Ts(s);let o;Es(s,i=>r(i),()=>r()),vs(i=>o=Rs(s,o,{class:i,...a}),[()=>tp("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t.class)]),Ms(s,n),ns(e,s),ys()}export{up as A,dp as T,kr as a,bd as b,cp as c,ip as d,_e as e,y as f,sd as g,Rr as h,zn as i,lp as j,tp as k,Rs as l,P as o,Ba as p,Ns as s,ep as t};
