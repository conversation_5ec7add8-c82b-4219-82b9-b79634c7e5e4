import{t as g,a as o,c as B,b as O}from"../chunks/Bc-255wv.js";import{p as ma,t as _,a as pa,c as d,r,s as F,b as X,d as _a,g as a,u as I,f as D,n as Z}from"../chunks/xwHhHbJ3.js";import{e as ga,s as y}from"../chunks/CJ0gORsW.js";import{i as f}from"../chunks/C6NPqmlb.js";import{e as aa,B as q,A as xa,i as ha}from"../chunks/DbSa5uNy.js";import{T as ya,s as ba}from"../chunks/Deiq1e4r.js";import{C as wa}from"../chunks/D6jtUsr0.js";var Ca=g('<div class="flex flex-col gap-2"> <div class="flex gap-2"><!> <!></div></div>'),ka=g('<div class="text-gray-500"> </div>'),Ta=g('<div class="text-gray-500">Getting location...</div>'),Ia=g('<div class="text-gray-500"> </div>'),La=g("<pre> </pre>"),Na=g('<div class="text-gray-500"> </div>'),Sa=g('<div class="text-gray-500"> </div>'),$a=g("<div></div>"),Fa=g('<main class="flex flex-col items-center h-dvh w-dvw"><div class="grid h-full w-full max-w-4xl grid-cols-1 grid-rows-[1fr,120px] p-2"><div class="overflow-y-auto w-full h-full"></div> <form class="relative"><p> </p> <div><a href="/chat/1">chat 1</a> <a href="/chat/2">chat 2</a> <a href="/chat/3">chat 3</a></div> <!> <!></form></div></main>');function Ga(ta,ea){ma(ea,!0);const b=new wa({async onToolCall({toolCall:t}){if(await new Promise(x=>setTimeout(x,2e3)),t.toolName==="getLocation"){const x=["New York","Los Angeles","Chicago","San Francisco"],w=x[Math.floor(Math.random()*x.length)];await b.addToolResult({toolCallId:t.toolCallId,tool:"getLocation",output:w})}}});let P=_a("");const oa=I(()=>b.status!=="ready");function sa(t){return t==="assistant"?"bg-primary text-secondary rounded-md":"bg-secondary text-primary rounded-md justify-self-end"}function H(t){t.preventDefault(),b.sendMessage({text:a(P)}),X(P,"")}var G=Fa(),Q=d(G),W=d(Q);aa(W,21,()=>b.messages,t=>t.id,(t,x)=>{var w=$a();aa(w,21,()=>a(x).parts,ha,(E,e)=>{var V=B(),la=D(V);{var va=C=>{var R=O();_(()=>y(R,a(e).text)),o(C,R)},na=(C,R)=>{{var da=k=>{var A=B();const M=I(()=>a(e).toolCallId),Y=I(()=>a(e).state);var h=D(A);{var T=i=>{var u=Ca();const l=I(()=>a(e).input);var v=d(u),c=F(v),s=d(c);q(s,{variant:"default",onclick:()=>b.addToolResult({toolCallId:a(M),tool:"askForConfirmation",output:"Yes, confirmed"}),children:(p,N)=>{Z();var m=O("Yes");o(p,m)},$$slots:{default:!0}});var n=F(s,2);q(n,{variant:"secondary",onclick:()=>b.addToolResult({toolCallId:a(M),tool:"askForConfirmation",output:"No, denied"}),children:(p,N)=>{Z();var m=O("No");o(p,m)},$$slots:{default:!0}}),r(c),r(u),_(()=>y(v,`${a(l).message??""} `)),o(i,u)},L=(i,u)=>{{var l=v=>{var c=ka(),s=d(c,!0);r(c),_(()=>y(s,a(e).output)),o(v,c)};f(i,v=>{a(Y)==="output-available"&&v(l)},u)}};f(h,i=>{a(Y)==="input-available"?i(T):i(L,!1)})}o(k,A)},fa=(k,A)=>{{var M=h=>{var T=B(),L=D(T);{var i=l=>{var v=Ta();o(l,v)},u=(l,v)=>{{var c=s=>{var n=Ia(),p=d(n);r(n),_(()=>y(p,`Location: ${a(e).output??""}`)),o(s,n)};f(l,s=>{a(e).state==="output-available"&&s(c)},v)}};f(L,l=>{a(e).state==="input-available"?l(i):l(u,!1)})}o(h,T)},Y=(h,T)=>{{var L=i=>{var u=B(),l=D(u);{var v=s=>{var n=La(),p=d(n,!0);r(n),_(N=>y(p,N),[()=>JSON.stringify(a(e),null,2)]),o(s,n)},c=(s,n)=>{{var p=m=>{var S=Na();const J=I(()=>a(e).input);var $=d(S);r(S),_(()=>y($,`Getting weather information for ${a(J).city??""}...`)),o(m,S)},N=(m,S)=>{{var J=$=>{var K=Sa();const ua=I(()=>a(e).input);var ca=d(K);r(K),_(()=>y(ca,`Weather in ${a(ua).city??""}: ${a(e).output??""}`)),o($,K)};f(m,$=>{a(e).state==="output-available"&&$(J)},S)}};f(s,m=>{a(e).state==="input-available"?m(p):m(N,!1)},n)}};f(l,s=>{a(e).state==="input-streaming"?s(v):s(c,!1)})}o(i,u)};f(h,i=>{a(e).type==="tool-getWeatherInformation"&&i(L)},T)}};f(k,h=>{a(e).type==="tool-getLocation"?h(M):h(Y,!1)},A)}};f(C,k=>{a(e).type==="tool-askForConfirmation"?k(da):k(fa,!1)},R)}};f(la,C=>{a(e).type==="text"?C(va):C(na,!1)})}o(E,V)}),r(w),_(E=>ba(w,1,`${E??""} my-2 max-w-[80%] p-2 flex flex-col gap-2`),[()=>sa(a(x).role)]),o(t,w)}),r(W);var j=F(W,2),z=d(j),ra=d(z,!0);r(z);var U=F(z,4);ya(U,{placeholder:"Send a message...",class:"h-full",onkeydown:t=>{t.key==="Enter"&&!t.shiftKey&&(t.preventDefault(),H(t))},get value(){return a(P)},set value(t){X(P,t,!0)}});var ia=F(U,2);q(ia,{"aria-label":"Send message",get disabled(){return a(oa)},type:"submit",size:"icon",class:"absolute right-3 bottom-3",children:(t,x)=>{xa(t,{})},$$slots:{default:!0}}),r(j),r(Q),r(G),_(()=>y(ra,b.status)),ga("submit",j,H),o(ta,G),pa()}export{Ga as component};
