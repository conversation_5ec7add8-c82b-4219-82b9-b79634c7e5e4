import{_ as I,C as w,ac as P,aq as V,au as m,av as E,aw as C,ae as D,ax as z,ay as H,F as Y,az as L,Q as x,at as F,aA as k,U as b,T as O,O as U,D as h,ai as $,aB as G,aC as Q,ak as J,aD as K,ab as X,z as Z,p as ee,h as te,a as re}from"./xwHhHbJ3.js";import{d as ae}from"./Bc-255wv.js";function _e(e){return e.endsWith("capture")&&e!=="gotpointercapture"&&e!=="lostpointercapture"}const oe=["beforeinput","click","change","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"];function pe(e){return oe.includes(e)}const ne={formnovalidate:"formNoValidate",ismap:"isMap",nomodule:"noModule",playsinline:"playsInline",readonly:"readOnly",defaultvalue:"defaultValue",defaultchecked:"defaultChecked",srcobject:"srcObject",novalidate:"noValidate",allowfullscreen:"allowFullscreen",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback"};function ve(e){return e=e.toLowerCase(),ne[e]??e}const ie=["touchstart","touchmove"];function se(e){return ie.includes(e)}function he(e,t){if(t){const r=document.body;e.autofocus=!0,I(()=>{document.activeElement===r&&e.focus()})}}function ye(e){w&&P(e)!==null&&V(e)}let R=!1;function ue(){R||(R=!0,document.addEventListener("reset",e=>{Promise.resolve().then(()=>{var t;if(!e.defaultPrevented)for(const r of e.target.elements)(t=r.__on_r)==null||t.call(r)})},{capture:!0}))}function M(e){var t=C,r=D;m(null),E(null);try{return e()}finally{m(t),E(r)}}function ge(e,t,r,i=r){e.addEventListener(t,()=>M(r));const o=e.__on_r;o?e.__on_r=()=>{o(),i(!0)}:e.__on_r=()=>i(!0),ue()}const B=new Set,S=new Set;function ce(e,t,r,i={}){function o(a){if(i.capture||y.call(t,a),!a.cancelBubble)return M(()=>r==null?void 0:r.call(this,a))}return e.startsWith("pointer")||e.startsWith("touch")||e==="wheel"?I(()=>{t.addEventListener(e,o,i)}):t.addEventListener(e,o,i),o}function be(e,t,r,i,o){var a={capture:i,passive:o},u=ce(e,t,r,a);(t===document.body||t===window||t===document)&&z(()=>{t.removeEventListener(e,u,a)})}function we(e){for(var t=0;t<e.length;t++)B.add(e[t]);for(var r of S)r(e)}function y(e){var N;var t=this,r=t.ownerDocument,i=e.type,o=((N=e.composedPath)==null?void 0:N.call(e))||[],a=o[0]||e.target,u=0,_=e.__root;if(_){var f=o.indexOf(_);if(f!==-1&&(t===document||t===window)){e.__root=t;return}var p=o.indexOf(t);if(p===-1)return;f<=p&&(u=f)}if(a=o[u]||e.target,a!==t){H(e,"currentTarget",{configurable:!0,get(){return a||r}});var T=C,c=D;m(null),E(null);try{for(var n,s=[];a!==null;){var l=a.assignedSlot||a.parentNode||a.host||null;try{var d=a["__"+i];if(d!=null&&(!a.disabled||e.target===a))if(Y(d)){var[j,...q]=d;j.apply(a,[e,...q])}else d.call(a,e)}catch(g){n?s.push(g):n=g}if(e.cancelBubble||l===t||l===null)break;a=l}if(n){for(let g of s)queueMicrotask(()=>{throw g});throw n}}finally{e.__root=t,delete e.currentTarget,m(T),E(c)}}}function me(e,t){var r=t==null?"":typeof t=="object"?t+"":t;r!==(e.__t??(e.__t=e.nodeValue))&&(e.__t=r,e.nodeValue=r+"")}function le(e,t){return W(e,t)}function Ee(e,t){L(),t.intro=t.intro??!1;const r=t.target,i=w,o=h;try{for(var a=P(r);a&&(a.nodeType!==8||a.data!==x);)a=F(a);if(!a)throw k;b(!0),O(a),U();const u=W(e,{...t,anchor:a});if(h===null||h.nodeType!==8||h.data!==$)throw G(),k;return b(!1),u}catch(u){if(u===k)return t.recover===!1&&Q(),L(),V(r),b(!1),le(e,t);throw u}finally{b(i),O(o)}}const v=new Map;function W(e,{target:t,anchor:r,props:i={},events:o,context:a,intro:u=!0}){L();var _=new Set,f=c=>{for(var n=0;n<c.length;n++){var s=c[n];if(!_.has(s)){_.add(s);var l=se(s);t.addEventListener(s,y,{passive:l});var d=v.get(s);d===void 0?(document.addEventListener(s,y,{passive:l}),v.set(s,1)):v.set(s,d+1)}}};f(J(B)),S.add(f);var p=void 0,T=K(()=>{var c=r??t.appendChild(X());return Z(()=>{if(a){ee({});var n=te;n.c=a}o&&(i.$$events=o),w&&ae(c,null),p=e(c,i)||{},w&&(D.nodes_end=h),a&&re()}),()=>{var l;for(var n of _){t.removeEventListener(n,y);var s=v.get(n);--s===0?(document.removeEventListener(n,y),v.delete(n)):v.set(n,s)}S.delete(f),c!==r&&((l=c.parentNode)==null||l.removeChild(c))}});return A.set(p,T),p}let A=new WeakMap;function Te(e,t){const r=A.get(e);return r?(A.delete(e),r(t)):Promise.resolve()}export{he as a,pe as b,ce as c,we as d,be as e,Ee as h,_e as i,ge as l,le as m,ve as n,ye as r,me as s,Te as u};
