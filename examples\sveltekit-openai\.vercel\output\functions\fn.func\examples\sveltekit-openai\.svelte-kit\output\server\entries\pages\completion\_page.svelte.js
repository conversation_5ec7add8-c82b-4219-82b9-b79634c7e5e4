import { q as copy_payload, t as assign_payload, m as pop, p as push, n as escape_html } from "../../../chunks/context.js";
import "clsx";
import { T as Textarea } from "../../../chunks/textarea.js";
import { g as generateId, u as callCompletionApi } from "../../../chunks/index.js";
import { g as getCompletionContext, K as KeyedCompletionStore, h as hasCompletionContext } from "../../../chunks/completion-context.svelte.js";
class Completion {
  #options = {};
  #api = this.#options.api ?? "/api/completion";
  #id = this.#options.id ?? generateId();
  #streamProtocol = this.#options.streamProtocol ?? "data";
  #keyedStore;
  #store = this.#keyedStore.get(this.#id);
  #abortController;
  /** The current completion result */
  get completion() {
    return this.#store.completions.get(this.#id) ?? "";
  }
  set completion(value) {
    this.#store.completions.set(this.#id, value);
  }
  /** The error object of the API request */
  get error() {
    return this.#store.error;
  }
  /** The current value of the input. Writable, so it can be bound to form inputs. */
  input;
  /**
   * Flag that indicates whether an API request is in progress.
   */
  get loading() {
    return this.#store.loading;
  }
  constructor(options = {}) {
    this.#keyedStore = hasCompletionContext() ? getCompletionContext() : new KeyedCompletionStore();
    this.#options = options;
    this.completion = options.initialCompletion ?? "";
    this.input = options.initialInput ?? "";
  }
  /**
   * Abort the current request immediately, keep the generated tokens if any.
   */
  stop = () => {
    try {
      this.#abortController?.abort();
    } catch {
    } finally {
      this.#store.loading = false;
      this.#abortController = void 0;
    }
  };
  /**
   * Send a new prompt to the API endpoint and update the completion state.
   */
  complete = async (prompt, options) => this.#triggerRequest(prompt, options);
  /** Form submission handler to automatically reset input and call the completion API */
  handleSubmit = async (event) => {
    event?.preventDefault?.();
    if (this.input) {
      await this.complete(this.input);
    }
  };
  #triggerRequest = async (prompt, options) => {
    return callCompletionApi({
      api: this.#api,
      prompt,
      credentials: this.#options.credentials,
      headers: { ...this.#options.headers, ...options?.headers },
      body: { ...this.#options.body, ...options?.body },
      streamProtocol: this.#streamProtocol,
      fetch: this.#options.fetch,
      // throttle streamed ui updates:
      setCompletion: (completion) => {
        this.completion = completion;
      },
      setLoading: (loading) => {
        this.#store.loading = loading;
      },
      setError: (error) => {
        this.#store.error = error;
      },
      setAbortController: (abortController) => {
        this.#abortController = abortController ?? void 0;
      },
      onFinish: this.#options.onFinish,
      onError: this.#options.onError
    });
  };
}
function _page($$payload, $$props) {
  push();
  const completion = new Completion();
  const submit = debounced(completion.handleSubmit, 300);
  function handleKeydown(event) {
    if (event.key === "Tab" && completion.completion) {
      event.preventDefault();
      completion.input += " " + completion.completion;
      completion.completion = "";
    }
    submit(event);
  }
  function debounced(fn, delay) {
    let timeoutId;
    return function(...args) {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => fn(...args), delay);
    };
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<main class="flex flex-col items-center h-dvh w-dvw"><div class="relative m-3 flex h-full w-full max-w-4xl grid-cols-1 grid-rows-[1fr,120px]"><div class="pointer-events-none absolute inset-0 -z-10 flex h-full min-h-[80px] w-full rounded-md border border-input bg-secondary px-3 py-2 text-base text-primary/50 ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 md:text-sm">`;
    if (completion.completion) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `${escape_html(completion.input + " " + completion.completion)}`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--></div> `;
    Textarea($$payload2, {
      placeholder: "Start typing to generate autocompletions...",
      class: "h-full bg-transparent",
      onkeydown: handleKeydown,
      get value() {
        return completion.input;
      },
      set value($$value) {
        completion.input = $$value;
        $$settled = false;
      }
    });
    $$payload2.out += `<!----></div></main>`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}
export {
  _page as default
};
