<script lang="ts">
  import { marked } from 'marked';

  interface Props {
    content?: string;
    animationDelay?: number;
    fadeInDuration?: number;
  }

  let {
    content = '',
    animationDelay = 30,
    fadeInDuration = 500
  }: Props = $props();

  let previousContent = '';
  let renderedHtml = $state('');
  let currentCharIndex = 0;

  // 配置 marked 选项
  marked.setOptions({
    breaks: true,
    gfm: true,
  });

  // 将文本分割成带动画的字符
  function wrapTextWithAnimation(text: string, startIndex: number = 0): string {
    return text.split('').map((char, index) => {
      const charIndex = startIndex + index;
      const displayChar = char === ' ' ? '&nbsp;' : char === '<' ? '&lt;' : char === '>' ? '&gt;' : char === '&' ? '&amp;' : char;
      return `<span class="fade-in-char" style="animation-delay: ${charIndex * animationDelay}ms; animation-duration: ${fadeInDuration}ms;">${displayChar}</span>`;
    }).join('');
  }

  // 处理 HTML 内容，为文本节点添加动画
  function processHtmlForAnimation(html: string, startCharIndex: number = 0): { processedHtml: string; charCount: number } {
    let charCount = 0;

    // 使用正则表达式匹配 HTML 标签和文本内容
    const processedHtml = html.replace(/(<[^>]*>)|([^<]+)/g, (match, tag, text) => {
      if (tag) {
        // 如果是 HTML 标签，直接返回
        return tag;
      } else if (text) {
        // 如果是文本内容，添加动画包装
        const wrappedText = wrapTextWithAnimation(text, startCharIndex + charCount);
        charCount += text.length;
        return wrappedText;
      }
      return match;
    });

    return { processedHtml, charCount };
  }

  // 处理新增内容
  async function processNewContent() {
    if (content === previousContent) return;

    // 获取新增的内容
    const newContent = content.slice(previousContent.length);
    if (!newContent) return;

    try {
      // 解析完整的 markdown 内容
      const fullHtml = await marked.parse(content);

      // 处理 HTML 内容，添加动画
      const { processedHtml } = processHtmlForAnimation(fullHtml, 0);

      // 更新渲染内容
      renderedHtml = processedHtml;
      currentCharIndex = content.length;
      previousContent = content;

    } catch (error) {
      console.error('Markdown parsing error:', error);
      // 如果解析失败，直接显示原始文本
      const { processedHtml } = processHtmlForAnimation(content, 0);
      renderedHtml = processedHtml;
      previousContent = content;
    }
  }

  // 响应内容变化
  $effect(() => {
    processNewContent();
  });
</script>

<div class="streaming-markdown">
  {@html renderedHtml}
</div>

<style>
  :global(.streaming-markdown) {
    line-height: 1.6;
  }

  :global(.streaming-markdown h1) {
    font-size: 2em;
    font-weight: bold;
    margin: 0.67em 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.3em;
  }

  :global(.streaming-markdown h2) {
    font-size: 1.5em;
    font-weight: bold;
    margin: 0.83em 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.3em;
  }

  :global(.streaming-markdown h3) {
    font-size: 1.17em;
    font-weight: bold;
    margin: 1em 0;
  }

  :global(.streaming-markdown h4) {
    font-size: 1em;
    font-weight: bold;
    margin: 1.33em 0;
  }

  :global(.streaming-markdown h5) {
    font-size: 0.83em;
    font-weight: bold;
    margin: 1.67em 0;
  }

  :global(.streaming-markdown h6) {
    font-size: 0.67em;
    font-weight: bold;
    margin: 2.33em 0;
  }

  :global(.streaming-markdown p) {
    margin: 1em 0;
  }

  :global(.streaming-markdown ul, .streaming-markdown ol) {
    margin: 1em 0;
    padding-left: 2em;
  }

  :global(.streaming-markdown li) {
    margin: 0.5em 0;
  }

  :global(.streaming-markdown blockquote) {
    margin: 1em 0;
    padding: 0 1em;
    border-left: 4px solid #ddd;
    color: #666;
  }

  :global(.streaming-markdown code) {
    background-color: #f5f5f5;
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.9em;
  }

  :global(.streaming-markdown pre) {
    background-color: #f5f5f5;
    padding: 1em;
    border-radius: 5px;
    overflow-x: auto;
    margin: 1em 0;
  }

  :global(.streaming-markdown pre code) {
    background-color: transparent;
    padding: 0;
  }

  :global(.streaming-markdown table) {
    border-collapse: collapse;
    width: 100%;
    margin: 1em 0;
  }

  :global(.streaming-markdown th, .streaming-markdown td) {
    border: 1px solid #ddd;
    padding: 0.5em;
    text-align: left;
  }

  :global(.streaming-markdown th) {
    background-color: #f5f5f5;
    font-weight: bold;
  }

  :global(.streaming-markdown a) {
    color: #0066cc;
    text-decoration: underline;
  }

  :global(.streaming-markdown a:hover) {
    color: #0052a3;
  }

  :global(.streaming-markdown strong) {
    font-weight: bold;
  }

  :global(.streaming-markdown em) {
    font-style: italic;
  }

  :global(.streaming-markdown hr) {
    border: none;
    border-top: 1px solid #ddd;
    margin: 2em 0;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(5px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  :global(.fade-in-char) {
    display: inline-block;
    opacity: 0;
    animation: fadeIn forwards;
    line-height: 1.6;
  }
</style>
