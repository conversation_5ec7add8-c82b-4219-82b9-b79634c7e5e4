var A=Object.getPrototypeOf;var B=Reflect.get;var j=t=>{throw TypeError(t)};var C=(t,r,e)=>r.has(t)||j("Cannot "+e);var i=(t,r,e)=>(C(t,r,"read from private field"),e?e.call(t):r.get(t)),h=(t,r,e)=>r.has(t)?j("Cannot add the same private member more than once"):r instanceof WeakSet?r.add(t):r.set(t,e),E=(t,r,e,s)=>(C(t,r,"write to private field"),s?s.call(t,e):r.set(t,e),e),x=(t,r,e)=>(C(t,r,"access private method"),e);var _=(t,r,e)=>B(A(t),e,r);import{b as p,K as l,g as c,L as D,M as F,N as G,j as H}from"./xwHhHbJ3.js";function d(t){p(t,t.v+1)}var u,a,f,v,z;const w=class w extends Map{constructor(e){super();h(this,v);h(this,u,new Map);h(this,a,l(0));h(this,f,l(0));if(e){for(var[s,n]of e)super.set(s,n);i(this,f).v=super.size}}has(e){var s=i(this,u),n=s.get(e);if(n===void 0){var o=super.get(e);if(o!==void 0)n=l(0),s.set(e,n);else return c(i(this,a)),!1}return c(n),!0}forEach(e,s){x(this,v,z).call(this),super.forEach(e,s)}get(e){var s=i(this,u),n=s.get(e);if(n===void 0){var o=super.get(e);if(o!==void 0)n=l(0),s.set(e,n);else{c(i(this,a));return}}return c(n),super.get(e)}set(e,s){var S;var n=i(this,u),o=n.get(e),L=super.get(e),M=super.set(e,s),m=i(this,a);if(o===void 0)n.set(e,l(0)),p(i(this,f),super.size),d(m);else if(L!==s){d(o);var K=m.reactions===null?null:new Set(m.reactions),N=K===null||!((S=o.reactions)!=null&&S.every(q=>K.has(q)));N&&d(m)}return M}delete(e){var s=i(this,u),n=s.get(e),o=super.delete(e);return n!==void 0&&(s.delete(e),p(i(this,f),super.size),p(n,-1),d(i(this,a))),o}clear(){if(super.size!==0){super.clear();var e=i(this,u);p(i(this,f),0);for(var s of e.values())p(s,-1);d(i(this,a)),e.clear()}}keys(){return c(i(this,a)),super.keys()}values(){return x(this,v,z).call(this),super.values()}entries(){return x(this,v,z).call(this),super.entries()}[Symbol.iterator](){return this.entries()}get size(){return c(i(this,f)),super.size}};u=new WeakMap,a=new WeakMap,f=new WeakMap,v=new WeakSet,z=function(){c(i(this,a));var e=i(this,u);if(i(this,f).v!==e.size)for(var s of _(w.prototype,this,"keys").call(this))e.has(s)||e.set(s,l(0));for(var[,n]of i(this,u))c(n)};let b=w;function O(t){const r=Symbol(t);return{hasContext:()=>{var e;try{return G(r)}catch(s){if(typeof s=="object"&&s!==null&&"message"in s&&typeof s.message=="string"&&((e=s.message)!=null&&e.includes("lifecycle_outside_component")))return!1;throw s}},getContext:()=>F(r),setContext:e=>D(r,e)}}var g;class P extends b{constructor(e,s){super(s);h(this,g);E(this,g,e)}get(e){return super.get(e)??H(()=>this.set(e,new(i(this,g)))).get(e)}}g=new WeakMap;export{P as K,b as S,O as c};
