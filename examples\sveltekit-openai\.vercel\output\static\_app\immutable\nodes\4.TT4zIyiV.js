import{t as g,a as o,c as R,b as O}from"../chunks/Bc-255wv.js";import{p as ma,t as _,a as pa,c as d,b as X,g as a,s as $,d as _a,r,u as A,f as M,n as Z}from"../chunks/xwHhHbJ3.js";import{e as ga,s as y}from"../chunks/CJ0gORsW.js";import{i as f}from"../chunks/C6NPqmlb.js";import{e as aa,B as q,A as xa,i as ha}from"../chunks/DbSa5uNy.js";import{T as ya,s as ba}from"../chunks/Deiq1e4r.js";import{p as wa}from"../chunks/BOEJ-ppe.js";import{C as Ca}from"../chunks/D6jtUsr0.js";var ka=g('<div class="flex flex-col gap-2"> <div class="flex gap-2"><!> <!></div></div>'),Ta=g('<div class="text-gray-500"> </div>'),Ia=g('<div class="text-gray-500">Getting location...</div>'),Sa=g('<div class="text-gray-500"> </div>'),La=g("<pre> </pre>"),Na=g('<div class="text-gray-500"> </div>'),$a=g('<div class="text-gray-500"> </div>'),Fa=g("<div></div>"),Pa=g('<main class="flex flex-col items-center h-dvh w-dvw"><div class="grid h-full w-full max-w-4xl grid-cols-1 grid-rows-[1fr,120px] p-2"><div class="overflow-y-auto w-full h-full"></div> <form class="relative"><p> </p> <div><a href="/chat/1">chat 1</a> <a href="/chat/2">chat 2</a> <a href="/chat/3">chat 3</a></div> <!> <!></form></div></main>');function ja(ta,ea){ma(ea,!0);const b=new Ca({id:wa.params.id,async onToolCall({toolCall:e}){if(await new Promise(x=>setTimeout(x,2e3)),e.toolName==="getLocation"){const x=["New York","Los Angeles","Chicago","San Francisco"],w=x[Math.floor(Math.random()*x.length)];await b.addToolResult({toolCallId:e.toolCallId,tool:"getLocation",output:w})}}}),oa=A(()=>b.status!=="ready");function sa(e){return e==="assistant"?"bg-primary text-secondary rounded-md":"bg-secondary text-primary rounded-md justify-self-end"}let I=_a("");function H(e){console.log("handleSubmit",e),console.log("input",a(I)),e.preventDefault(),b.sendMessage({text:a(I)}),X(I,"")}var Y=Pa(),Q=d(Y),B=d(Q);aa(B,21,()=>b.messages,e=>e.id,(e,x)=>{var w=Fa();aa(w,21,()=>a(x).parts,ha,(W,t)=>{var V=R(),la=M(V);{var va=C=>{var F=O();_(()=>y(F,a(t).text)),o(C,F)},na=(C,F)=>{{var da=k=>{var P=R(),j=M(P);{var z=l=>{var c=ka();const m=A(()=>a(t).input);var v=d(c),s=$(v),u=d(s);q(u,{variant:"default",onclick:()=>b.addToolResult({toolCallId:a(t).toolCallId,tool:"askForConfirmation",output:"Yes, confirmed"}),children:(i,n)=>{Z();var p=O("Yes");o(i,p)},$$slots:{default:!0}});var S=$(u,2);q(S,{variant:"secondary",onclick:()=>b.addToolResult({toolCallId:a(t).toolCallId,tool:"askForConfirmation",output:"No, denied"}),children:(i,n)=>{Z();var p=O("No");o(i,p)},$$slots:{default:!0}}),r(s),r(c),_(()=>y(v,`${a(m).message??""} `)),o(l,c)},h=(l,c)=>{{var m=v=>{var s=Ta(),u=d(s,!0);r(s),_(()=>y(u,a(t).output)),o(v,s)};f(l,v=>{a(t).state==="output-available"&&v(m)},c)}};f(j,l=>{a(t).state==="input-available"?l(z):l(h,!1)})}o(k,P)},fa=(k,P)=>{{var j=h=>{var l=R(),c=M(l);{var m=s=>{var u=Ia();o(s,u)},v=(s,u)=>{{var S=i=>{var n=Sa(),p=d(n);r(n),_(()=>y(p,`Location: ${a(t).output??""}`)),o(i,n)};f(s,i=>{a(t).state==="output-available"&&i(S)},u)}};f(c,s=>{a(t).state==="input-available"?s(m):s(v,!1)})}o(h,l)},z=(h,l)=>{{var c=m=>{var v=R(),s=M(v);{var u=i=>{var n=La(),p=d(n,!0);r(n),_(E=>y(p,E),[()=>JSON.stringify(a(t),null,2)]),o(i,n)},S=(i,n)=>{{var p=T=>{var L=Na();const J=A(()=>a(t).input);var N=d(L);r(L),_(()=>y(N,`Getting weather information for ${a(J).city??""}...`)),o(T,L)},E=(T,L)=>{{var J=N=>{var K=$a();const ua=A(()=>a(t).input);var ca=d(K);r(K),_(()=>y(ca,`Weather in ${a(ua).city??""}: ${a(t).output??""}`)),o(N,K)};f(T,N=>{a(t).state==="output-available"&&N(J)},L)}};f(i,T=>{a(t).state==="input-available"?T(p):T(E,!1)},n)}};f(s,i=>{a(t).state==="input-streaming"?i(u):i(S,!1)})}o(m,v)};f(h,m=>{a(t).type==="tool-getWeatherInformation"&&m(c)},l)}};f(k,h=>{a(t).type==="tool-getLocation"?h(j):h(z,!1)},P)}};f(C,k=>{a(t).type==="tool-askForConfirmation"?k(da):k(fa,!1)},F)}};f(la,C=>{a(t).type==="text"?C(va):C(na,!1)})}o(W,V)}),r(w),_(W=>ba(w,1,`${W??""} my-2 max-w-[80%] p-2 flex flex-col gap-2`),[()=>sa(a(x).role)]),o(e,w)}),r(B);var D=$(B,2),G=d(D),ia=d(G,!0);r(G);var U=$(G,4);ya(U,{placeholder:"Send a message...",class:"h-full",onkeydown:e=>{e.key==="Enter"&&!e.shiftKey&&(e.preventDefault(),H(e))},get value(){return a(I)},set value(e){X(I,e,!0)}});var ra=$(U,2);q(ra,{"aria-label":"Send message",get disabled(){return a(oa)},type:"submit",size:"icon",class:"absolute right-3 bottom-3",children:(e,x)=>{xa(e,{})},$$slots:{default:!0}}),r(D),r(Q),r(Y),_(()=>y(ia,b.status)),ga("submit",D,H),o(ta,Y),pa()}export{ja as component};
