<script>
  export let text = '';
</script>

{#if text}
  <span>
    {#each text.split('') as char, i}
      <span class="fade-in-char" style="animation-delay: {i * 20}ms;">
        {char === ' ' ? '\u00A0' : char}
      </span>
    {/each}
  </span>
{/if}

<style>
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(5px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .fade-in-char {
    display: inline-block; /* 确保 transform 生效 */
    opacity: 0;
    animation: fadeIn 0.4s forwards;
    /* 防止在快速流动时出现布局抖动 */
    line-height: 1.5; 
  }
</style>
