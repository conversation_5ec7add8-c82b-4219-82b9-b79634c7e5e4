var Y=Object.defineProperty;var K=a=>{throw TypeError(a)};var Z=(a,e,t)=>e in a?Y(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t;var k=(a,e,t)=>Z(a,typeof e!="symbol"?e+"":e,t),J=(a,e,t)=>e.has(a)||K("Cannot "+t);var r=(a,e,t)=>(J(a,e,"read from private field"),t?t.call(a):e.get(a)),u=(a,e,t)=>e.has(a)?K("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(a):e.set(a,t),g=(a,e,t,o)=>(J(a,e,"write to private field"),o?o.call(a,t):e.set(a,t),t);import{n as ee,a as T,t as A}from"../chunks/Bc-255wv.js";import{u as N,d as M,g as i,b as _,t as C,p as te,a as re,s as E,c as p,r as m}from"../chunks/xwHhHbJ3.js";import{e as ae,s as q}from"../chunks/CJ0gORsW.js";import{p as se,i as ie}from"../chunks/C6NPqmlb.js";import{e as oe,B as H,A as ne,i as le}from"../chunks/DbSa5uNy.js";import{a as ce,b as de,p as he,i as ue,d as fe,g as pe,o as I,e as me,f as R,h as W,T as ve}from"../chunks/Deiq1e4r.js";import{h as be,g as ge,K as we}from"../chunks/DblLzjgS.js";var l,$,w,n,v,O,G,S;class ye{constructor(e){u(this,O);u(this,l,{});u(this,$,N(()=>r(this,l).id??pe()));u(this,w,M());u(this,n,N(()=>i(r(this,w)).get(i(r(this,$)))));u(this,v);k(this,"stop",()=>{var e;try{(e=r(this,v))==null||e.abort()}catch{}finally{i(r(this,n)).loading=!1,g(this,v,void 0)}});k(this,"submit",async e=>{try{r(this,S).call(this),i(r(this,n)).loading=!0;const t=new AbortController;g(this,v,t);const c=await(r(this,l).fetch??fetch)(r(this,l).api,{method:"POST",headers:{"Content-Type":"application/json",...r(this,l).headers},credentials:r(this,l).credentials,signal:t.signal,body:JSON.stringify(e)});if(!c.ok)throw new Error(await c.text()??"Failed to fetch the response.");if(c.body==null)throw new Error("The response body is empty.");let y="",f;await c.body.pipeThrough(new TextDecoderStream).pipeTo(new WritableStream({write:async d=>{if(t!=null&&t.signal.aborted)throw new DOMException("Stream aborted","AbortError");y+=d;const{value:j}=await he(y),b=j;ue(f,b)||(f=b,i(r(this,n)).object=b)},close:async()=>{if(i(r(this,n)).loading=!1,g(this,v,void 0),r(this,l).onFinish!=null){const d=await ce({value:f,schema:de(r(this,l).schema)});r(this,l).onFinish(d.success?{object:d.value,error:void 0}:{object:void 0,error:d.error})}}}))}catch(t){if(fe(t))return;const o=t instanceof Error?t:new Error(String(t));r(this,l).onError&&r(this,l).onError(o),i(r(this,n)).loading=!1,i(r(this,n)).error=o}});k(this,"clear",()=>{this.stop(),r(this,S).call(this)});u(this,S,()=>{i(r(this,n)).object=void 0,i(r(this,n)).error=void 0,i(r(this,n)).loading=!1});be()?_(r(this,w),ge(),!0):_(r(this,w),new we,!0),g(this,l,e),g(this,O,e.initialValue,G)}get object(){return i(r(this,n)).object}get error(){return i(r(this,n)).error}get loading(){return i(r(this,n)).loading}}l=new WeakMap,$=new WeakMap,w=new WeakMap,n=new WeakMap,v=new WeakMap,O=new WeakSet,G=function(e){i(r(this,n)).object=e},S=new WeakMap;const je=I({notifications:me(I({name:R().describe("Name of a fictional person."),message:R().describe("Message. Do not use emojis or links.")}))});var xe=ee('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash-icon lucide-trash"><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path><path d="M3 6h18"></path><path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path></svg>');function _e(a,e){let t=se(e,"size",3,16);var o=xe();C(()=>{W(o,"width",t()),W(o,"height",t())}),T(a,o)}var Se=A('<div class="my-2 max-w-[80%] justify-self-end rounded-md bg-secondary p-2 text-primary"> </div>'),ke=A('<div class="my-2 max-w-[80%] rounded-md bg-primary p-2 text-secondary"> </div>'),Ee=A('<main class="flex flex-col items-center h-dvh w-dvw"><div class="grid h-full w-full max-w-4xl grid-cols-1 grid-rows-[1fr,120px] p-2"><div class="w-full h-full overflow-y-auto"><!> <!></div> <form class="relative"><!> <div class="absolute bottom-3 right-3"><!> <!></div></form></div></main>');function ze(a,e){te(e,!0);const t=new ye({api:"/api/structured-object",schema:je});let o=M(""),c=M("");function y(s){_(c,i(o),!0),s.preventDefault(),t.submit(i(o)),_(o,"")}var f=Ee(),d=p(f),j=p(d),b=p(j);{var L=s=>{var h=Se(),x=p(h);m(h),C(()=>q(x,`Me: ${i(c)??""}`)),T(s,h)};ie(b,s=>{i(c)&&s(L)})}var Q=E(b,2);oe(Q,17,()=>{var s;return((s=t.object)==null?void 0:s.notifications)??[]},le,(s,h)=>{var x=ke(),X=p(x);m(x),C(()=>{var V,B;return q(X,`${((V=i(h))==null?void 0:V.name)??""}: ${((B=i(h))==null?void 0:B.message)??""}`)}),T(s,x)}),m(j);var D=E(j,2),F=p(D);ve(F,{placeholder:"Think of a theme to generate three notifications...",class:"h-full",onkeydown:s=>{s.key==="Enter"&&!s.shiftKey&&(s.preventDefault(),y(s))},get value(){return i(o)},set value(s){_(o,s,!0)}});var z=E(F,2),P=p(z);H(P,{"aria-label":"Clear",type:"button",size:"icon",onclick:()=>t.clear(),children:(s,h)=>{_e(s,{})},$$slots:{default:!0}});var U=E(P,2);H(U,{"aria-label":"Send message",get disabled(){return t.loading},type:"submit",size:"icon",children:(s,h)=>{ne(s,{})},$$slots:{default:!0}}),m(z),m(D),m(d),m(f),ae("submit",D,s=>{s.preventDefault(),y(s)}),T(a,f),re()}export{ze as component};
