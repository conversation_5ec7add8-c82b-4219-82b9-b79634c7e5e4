

export const index = 5;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/completion/_page.svelte.js')).default;
export const imports = ["_app/immutable/nodes/5.wipIXV5g.js","_app/immutable/chunks/Bc-255wv.js","_app/immutable/chunks/xwHhHbJ3.js","_app/immutable/chunks/DPKhDFdk.js","_app/immutable/chunks/CJ0gORsW.js","_app/immutable/chunks/C6NPqmlb.js","_app/immutable/chunks/DteoZW-m.js","_app/immutable/chunks/Deiq1e4r.js","_app/immutable/chunks/YCrX7EkR.js","_app/immutable/chunks/CbGKKmWg.js"];
export const stylesheets = [];
export const fonts = [];
