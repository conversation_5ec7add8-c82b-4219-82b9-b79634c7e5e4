{"name": "@example/sveltekit-openai", "version": "0.0.0", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "type-check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "type-check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "prettier-fix": "prettier --write .", "prettier-check": "prettier --check .", "lint": "eslint .", "clean": "rm -rf .svelte-kit && rm -rf .vercel"}, "type": "module", "devDependencies": {"@ai-sdk/openai": "workspace:*", "@ai-sdk/provider-utils": "workspace:*", "@ai-sdk/svelte": "workspace:*", "@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@sveltejs/adapter-vercel": "^5.5.2", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@vercel/ai-tsconfig": "workspace:*", "ai": "workspace:*", "autoprefixer": "^10.4.20", "bits-ui": "^1.3.9", "clsx": "^2.1.1", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "svelte": "^5.31.0", "svelte-check": "^4.0.0", "tailwind-merge": "^3.0.2", "tailwind-variants": "^1.0.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.0.0", "zod": "3.25.76"}, "dependencies": {"@ai-sdk/deepseek": "workspace:^", "@ai-sdk/openai-compatible": "workspace:^", "marked": "^16.1.2"}}